{"version": 3, "sources": ["../../@radix-ui/react-focus-scope/src/focus-scope.tsx", "../../@radix-ui/react-focus-guards/src/focus-guards.tsx", "../../tslib/tslib.es6.mjs", "../../react-remove-scroll/dist/es2015/Combination.js", "../../react-remove-scroll/dist/es2015/UI.js", "../../react-remove-scroll-bar/dist/es2015/constants.js", "../../use-callback-ref/dist/es2015/assignRef.js", "../../use-callback-ref/dist/es2015/useRef.js", "../../use-callback-ref/dist/es2015/useMergeRef.js", "../../use-sidecar/dist/es2015/hoc.js", "../../use-sidecar/dist/es2015/hook.js", "../../use-sidecar/dist/es2015/medium.js", "../../use-sidecar/dist/es2015/renderProp.js", "../../use-sidecar/dist/es2015/exports.js", "../../react-remove-scroll/dist/es2015/medium.js", "../../react-remove-scroll/dist/es2015/SideEffect.js", "../../react-remove-scroll-bar/dist/es2015/component.js", "../../react-style-singleton/dist/es2015/hook.js", "../../get-nonce/dist/es2015/index.js", "../../react-style-singleton/dist/es2015/singleton.js", "../../react-style-singleton/dist/es2015/component.js", "../../react-remove-scroll-bar/dist/es2015/utils.js", "../../react-remove-scroll/dist/es2015/aggresiveCapture.js", "../../react-remove-scroll/dist/es2015/handleScroll.js", "../../react-remove-scroll/dist/es2015/sidecar.js", "../../aria-hidden/dist/es2015/index.js"], "sourcesContent": ["import * as React from 'react';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\n\nconst AUTOFOCUS_ON_MOUNT = 'focusScope.autoFocusOnMount';\nconst AUTOFOCUS_ON_UNMOUNT = 'focusScope.autoFocusOnUnmount';\nconst EVENT_OPTIONS = { bubbles: false, cancelable: true };\n\ntype FocusableTarget = HTMLElement | { focus(): void };\n\n/* -------------------------------------------------------------------------------------------------\n * FocusScope\n * -----------------------------------------------------------------------------------------------*/\n\nconst FOCUS_SCOPE_NAME = 'FocusScope';\n\ntype FocusScopeElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface FocusScopeProps extends PrimitiveDivProps {\n  /**\n   * When `true`, tabbing from last item will focus first tabbable\n   * and shift+tab from first item will focus last tababble.\n   * @defaultValue false\n   */\n  loop?: boolean;\n\n  /**\n   * When `true`, focus cannot escape the focus scope via keyboard,\n   * pointer, or a programmatic focus.\n   * @defaultValue false\n   */\n  trapped?: boolean;\n\n  /**\n   * Event handler called when auto-focusing on mount.\n   * Can be prevented.\n   */\n  onMountAutoFocus?: (event: Event) => void;\n\n  /**\n   * Event handler called when auto-focusing on unmount.\n   * Can be prevented.\n   */\n  onUnmountAutoFocus?: (event: Event) => void;\n}\n\nconst FocusScope = React.forwardRef<FocusScopeElement, FocusScopeProps>((props, forwardedRef) => {\n  const {\n    loop = false,\n    trapped = false,\n    onMountAutoFocus: onMountAutoFocusProp,\n    onUnmountAutoFocus: onUnmountAutoFocusProp,\n    ...scopeProps\n  } = props;\n  const [container, setContainer] = React.useState<HTMLElement | null>(null);\n  const onMountAutoFocus = useCallbackRef(onMountAutoFocusProp);\n  const onUnmountAutoFocus = useCallbackRef(onUnmountAutoFocusProp);\n  const lastFocusedElementRef = React.useRef<HTMLElement | null>(null);\n  const composedRefs = useComposedRefs(forwardedRef, (node) => setContainer(node));\n\n  const focusScope = React.useRef({\n    paused: false,\n    pause() {\n      this.paused = true;\n    },\n    resume() {\n      this.paused = false;\n    },\n  }).current;\n\n  // Takes care of trapping focus if focus is moved outside programmatically for example\n  React.useEffect(() => {\n    if (trapped) {\n      function handleFocusIn(event: FocusEvent) {\n        if (focusScope.paused || !container) return;\n        const target = event.target as HTMLElement | null;\n        if (container.contains(target)) {\n          lastFocusedElementRef.current = target;\n        } else {\n          focus(lastFocusedElementRef.current, { select: true });\n        }\n      }\n\n      function handleFocusOut(event: FocusEvent) {\n        if (focusScope.paused || !container) return;\n        const relatedTarget = event.relatedTarget as HTMLElement | null;\n\n        // A `focusout` event with a `null` `relatedTarget` will happen in at least two cases:\n        //\n        // 1. When the user switches app/tabs/windows/the browser itself loses focus.\n        // 2. In Google Chrome, when the focused element is removed from the DOM.\n        //\n        // We let the browser do its thing here because:\n        //\n        // 1. The browser already keeps a memory of what's focused for when the page gets refocused.\n        // 2. In Google Chrome, if we try to focus the deleted focused element (as per below), it\n        //    throws the CPU to 100%, so we avoid doing anything for this reason here too.\n        if (relatedTarget === null) return;\n\n        // If the focus has moved to an actual legitimate element (`relatedTarget !== null`)\n        // that is outside the container, we move focus to the last valid focused element inside.\n        if (!container.contains(relatedTarget)) {\n          focus(lastFocusedElementRef.current, { select: true });\n        }\n      }\n\n      // When the focused element gets removed from the DOM, browsers move focus\n      // back to the document.body. In this case, we move focus to the container\n      // to keep focus trapped correctly.\n      function handleMutations(mutations: MutationRecord[]) {\n        const focusedElement = document.activeElement as HTMLElement | null;\n        if (focusedElement !== document.body) return;\n        for (const mutation of mutations) {\n          if (mutation.removedNodes.length > 0) focus(container);\n        }\n      }\n\n      document.addEventListener('focusin', handleFocusIn);\n      document.addEventListener('focusout', handleFocusOut);\n      const mutationObserver = new MutationObserver(handleMutations);\n      if (container) mutationObserver.observe(container, { childList: true, subtree: true });\n\n      return () => {\n        document.removeEventListener('focusin', handleFocusIn);\n        document.removeEventListener('focusout', handleFocusOut);\n        mutationObserver.disconnect();\n      };\n    }\n  }, [trapped, container, focusScope.paused]);\n\n  React.useEffect(() => {\n    if (container) {\n      focusScopesStack.add(focusScope);\n      const previouslyFocusedElement = document.activeElement as HTMLElement | null;\n      const hasFocusedCandidate = container.contains(previouslyFocusedElement);\n\n      if (!hasFocusedCandidate) {\n        const mountEvent = new CustomEvent(AUTOFOCUS_ON_MOUNT, EVENT_OPTIONS);\n        container.addEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n        container.dispatchEvent(mountEvent);\n        if (!mountEvent.defaultPrevented) {\n          focusFirst(removeLinks(getTabbableCandidates(container)), { select: true });\n          if (document.activeElement === previouslyFocusedElement) {\n            focus(container);\n          }\n        }\n      }\n\n      return () => {\n        container.removeEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n\n        // We hit a react bug (fixed in v17) with focusing in unmount.\n        // We need to delay the focus a little to get around it for now.\n        // See: https://github.com/facebook/react/issues/17894\n        setTimeout(() => {\n          const unmountEvent = new CustomEvent(AUTOFOCUS_ON_UNMOUNT, EVENT_OPTIONS);\n          container.addEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n          container.dispatchEvent(unmountEvent);\n          if (!unmountEvent.defaultPrevented) {\n            focus(previouslyFocusedElement ?? document.body, { select: true });\n          }\n          // we need to remove the listener after we `dispatchEvent`\n          container.removeEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n\n          focusScopesStack.remove(focusScope);\n        }, 0);\n      };\n    }\n  }, [container, onMountAutoFocus, onUnmountAutoFocus, focusScope]);\n\n  // Takes care of looping focus (when tabbing whilst at the edges)\n  const handleKeyDown = React.useCallback(\n    (event: React.KeyboardEvent) => {\n      if (!loop && !trapped) return;\n      if (focusScope.paused) return;\n\n      const isTabKey = event.key === 'Tab' && !event.altKey && !event.ctrlKey && !event.metaKey;\n      const focusedElement = document.activeElement as HTMLElement | null;\n\n      if (isTabKey && focusedElement) {\n        const container = event.currentTarget as HTMLElement;\n        const [first, last] = getTabbableEdges(container);\n        const hasTabbableElementsInside = first && last;\n\n        // we can only wrap focus if we have tabbable edges\n        if (!hasTabbableElementsInside) {\n          if (focusedElement === container) event.preventDefault();\n        } else {\n          if (!event.shiftKey && focusedElement === last) {\n            event.preventDefault();\n            if (loop) focus(first, { select: true });\n          } else if (event.shiftKey && focusedElement === first) {\n            event.preventDefault();\n            if (loop) focus(last, { select: true });\n          }\n        }\n      }\n    },\n    [loop, trapped, focusScope.paused]\n  );\n\n  return (\n    <Primitive.div tabIndex={-1} {...scopeProps} ref={composedRefs} onKeyDown={handleKeyDown} />\n  );\n});\n\nFocusScope.displayName = FOCUS_SCOPE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * Utils\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * Attempts focusing the first element in a list of candidates.\n * Stops when focus has actually moved.\n */\nfunction focusFirst(candidates: HTMLElement[], { select = false } = {}) {\n  const previouslyFocusedElement = document.activeElement;\n  for (const candidate of candidates) {\n    focus(candidate, { select });\n    if (document.activeElement !== previouslyFocusedElement) return;\n  }\n}\n\n/**\n * Returns the first and last tabbable elements inside a container.\n */\nfunction getTabbableEdges(container: HTMLElement) {\n  const candidates = getTabbableCandidates(container);\n  const first = findVisible(candidates, container);\n  const last = findVisible(candidates.reverse(), container);\n  return [first, last] as const;\n}\n\n/**\n * Returns a list of potential tabbable candidates.\n *\n * NOTE: This is only a close approximation. For example it doesn't take into account cases like when\n * elements are not visible. This cannot be worked out easily by just reading a property, but rather\n * necessitate runtime knowledge (computed styles, etc). We deal with these cases separately.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/TreeWalker\n * Credit: https://github.com/discord/focus-layers/blob/master/src/util/wrapFocus.tsx#L1\n */\nfunction getTabbableCandidates(container: HTMLElement) {\n  const nodes: HTMLElement[] = [];\n  const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n    acceptNode: (node: any) => {\n      const isHiddenInput = node.tagName === 'INPUT' && node.type === 'hidden';\n      if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n      // `.tabIndex` is not the same as the `tabindex` attribute. It works on the\n      // runtime's understanding of tabbability, so this automatically accounts\n      // for any kind of element that could be tabbed to.\n      return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n    },\n  });\n  while (walker.nextNode()) nodes.push(walker.currentNode as HTMLElement);\n  // we do not take into account the order of nodes with positive `tabIndex` as it\n  // hinders accessibility to have tab order different from visual order.\n  return nodes;\n}\n\n/**\n * Returns the first visible element in a list.\n * NOTE: Only checks visibility up to the `container`.\n */\nfunction findVisible(elements: HTMLElement[], container: HTMLElement) {\n  for (const element of elements) {\n    // we stop checking if it's hidden at the `container` level (excluding)\n    if (!isHidden(element, { upTo: container })) return element;\n  }\n}\n\nfunction isHidden(node: HTMLElement, { upTo }: { upTo?: HTMLElement }) {\n  if (getComputedStyle(node).visibility === 'hidden') return true;\n  while (node) {\n    // we stop at `upTo` (excluding it)\n    if (upTo !== undefined && node === upTo) return false;\n    if (getComputedStyle(node).display === 'none') return true;\n    node = node.parentElement as HTMLElement;\n  }\n  return false;\n}\n\nfunction isSelectableInput(element: any): element is FocusableTarget & { select: () => void } {\n  return element instanceof HTMLInputElement && 'select' in element;\n}\n\nfunction focus(element?: FocusableTarget | null, { select = false } = {}) {\n  // only focus if that element is focusable\n  if (element && element.focus) {\n    const previouslyFocusedElement = document.activeElement;\n    // NOTE: we prevent scrolling on focus, to minimize jarring transitions for users\n    element.focus({ preventScroll: true });\n    // only select if its not the same element, it supports selection and we need to select\n    if (element !== previouslyFocusedElement && isSelectableInput(element) && select)\n      element.select();\n  }\n}\n\n/* -------------------------------------------------------------------------------------------------\n * FocusScope stack\n * -----------------------------------------------------------------------------------------------*/\n\ntype FocusScopeAPI = { paused: boolean; pause(): void; resume(): void };\nconst focusScopesStack = createFocusScopesStack();\n\nfunction createFocusScopesStack() {\n  /** A stack of focus scopes, with the active one at the top */\n  let stack: FocusScopeAPI[] = [];\n\n  return {\n    add(focusScope: FocusScopeAPI) {\n      // pause the currently active focus scope (at the top of the stack)\n      const activeFocusScope = stack[0];\n      if (focusScope !== activeFocusScope) {\n        activeFocusScope?.pause();\n      }\n      // remove in case it already exists (because we'll re-add it at the top of the stack)\n      stack = arrayRemove(stack, focusScope);\n      stack.unshift(focusScope);\n    },\n\n    remove(focusScope: FocusScopeAPI) {\n      stack = arrayRemove(stack, focusScope);\n      stack[0]?.resume();\n    },\n  };\n}\n\nfunction arrayRemove<T>(array: T[], item: T) {\n  const updatedArray = [...array];\n  const index = updatedArray.indexOf(item);\n  if (index !== -1) {\n    updatedArray.splice(index, 1);\n  }\n  return updatedArray;\n}\n\nfunction removeLinks(items: HTMLElement[]) {\n  return items.filter((item) => item.tagName !== 'A');\n}\n\nconst Root = FocusScope;\n\nexport {\n  FocusScope,\n  //\n  Root,\n};\nexport type { FocusScopeProps };\n", "import * as React from 'react';\n\n/** Number of components which have requested interest to have focus guards */\nlet count = 0;\n\nfunction FocusGuards(props: any) {\n  useFocusGuards();\n  return props.children;\n}\n\n/**\n * Injects a pair of focus guards at the edges of the whole DOM tree\n * to ensure `focusin` & `focusout` events can be caught consistently.\n */\nfunction useFocusGuards() {\n  React.useEffect(() => {\n    const edgeGuards = document.querySelectorAll('[data-radix-focus-guard]');\n    document.body.insertAdjacentElement('afterbegin', edgeGuards[0] ?? createFocusGuard());\n    document.body.insertAdjacentElement('beforeend', edgeGuards[1] ?? createFocusGuard());\n    count++;\n\n    return () => {\n      if (count === 1) {\n        document.querySelectorAll('[data-radix-focus-guard]').forEach((node) => node.remove());\n      }\n      count--;\n    };\n  }, []);\n}\n\nfunction createFocusGuard() {\n  const element = document.createElement('span');\n  element.setAttribute('data-radix-focus-guard', '');\n  element.tabIndex = 0;\n  element.style.outline = 'none';\n  element.style.opacity = '0';\n  element.style.position = 'fixed';\n  element.style.pointerEvents = 'none';\n  return element;\n}\n\nconst Root = FocusGuards;\n\nexport {\n  FocusGuards,\n  //\n  Root,\n  //\n  useFocusGuards,\n};\n", "/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nexport function __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nexport var __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nexport function __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nexport function __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nexport function __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nexport function __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nexport function __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nexport function __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nexport function __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nexport function __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nexport function __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n  return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nexport function __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nexport function __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nexport function __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nexport function __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nexport function __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nexport function __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nexport function __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nexport function __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n  function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nexport function __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nexport function __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nexport function __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nvar ownKeys = function(o) {\n  ownKeys = Object.getOwnPropertyNames || function (o) {\n    var ar = [];\n    for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n    return ar;\n  };\n  return ownKeys(o);\n};\n\nexport function __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nexport function __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nexport function __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nexport function __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose, inner;\n    if (async) {\n      if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n      dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n      if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n      dispose = value[Symbol.dispose];\n      if (async) inner = dispose;\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nexport function __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  var r, s = 0;\n  function next() {\n    while (r = env.stack.pop()) {\n      try {\n        if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\n        if (r.dispose) {\n          var result = r.dispose.call(r.value);\n          if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n        }\n        else s |= 1;\n      }\n      catch (e) {\n        fail(e);\n      }\n    }\n    if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nexport function __rewriteRelativeImportExtension(path, preserveJsx) {\n  if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\n      return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {\n          return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : (d + ext + \".\" + cm.toLowerCase() + \"js\");\n      });\n  }\n  return path;\n}\n\nexport default {\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __esDecorate,\n  __runInitializers,\n  __propKey,\n  __setFunctionName,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n  __rewriteRelativeImportExtension,\n};\n", "import { __assign } from \"tslib\";\nimport * as React from 'react';\nimport { RemoveScroll } from './UI';\nimport SideCar from './sidecar';\nvar ReactRemoveScroll = React.forwardRef(function (props, ref) { return (React.createElement(RemoveScroll, __assign({}, props, { ref: ref, sideCar: SideCar }))); });\nReactRemoveScroll.classNames = RemoveScroll.classNames;\nexport default ReactRemoveScroll;\n", "import { __assign, __rest } from \"tslib\";\nimport * as React from 'react';\nimport { fullWidthClassName, zeroRightClassName } from 'react-remove-scroll-bar/constants';\nimport { useMergeRefs } from 'use-callback-ref';\nimport { effectCar } from './medium';\nvar nothing = function () {\n    return;\n};\n/**\n * Removes scrollbar from the page and contain the scroll within the Lock\n */\nvar RemoveScroll = React.forwardRef(function (props, parentRef) {\n    var ref = React.useRef(null);\n    var _a = React.useState({\n        onScrollCapture: nothing,\n        onWheelCapture: nothing,\n        onTouchMoveCapture: nothing,\n    }), callbacks = _a[0], setCallbacks = _a[1];\n    var forwardProps = props.forwardProps, children = props.children, className = props.className, removeScrollBar = props.removeScrollBar, enabled = props.enabled, shards = props.shards, sideCar = props.sideCar, noRelative = props.noRelative, noIsolation = props.noIsolation, inert = props.inert, allowPinchZoom = props.allowPinchZoom, _b = props.as, Container = _b === void 0 ? 'div' : _b, gapMode = props.gapMode, rest = __rest(props, [\"forwardProps\", \"children\", \"className\", \"removeScrollBar\", \"enabled\", \"shards\", \"sideCar\", \"noRelative\", \"noIsolation\", \"inert\", \"allowPinchZoom\", \"as\", \"gapMode\"]);\n    var SideCar = sideCar;\n    var containerRef = useMergeRefs([ref, parentRef]);\n    var containerProps = __assign(__assign({}, rest), callbacks);\n    return (React.createElement(React.Fragment, null,\n        enabled && (React.createElement(SideCar, { sideCar: effectCar, removeScrollBar: removeScrollBar, shards: shards, noRelative: noRelative, noIsolation: noIsolation, inert: inert, setCallbacks: setCallbacks, allowPinchZoom: !!allowPinchZoom, lockRef: ref, gapMode: gapMode })),\n        forwardProps ? (React.cloneElement(React.Children.only(children), __assign(__assign({}, containerProps), { ref: containerRef }))) : (React.createElement(Container, __assign({}, containerProps, { className: className, ref: containerRef }), children))));\n});\nRemoveScroll.defaultProps = {\n    enabled: true,\n    removeScrollBar: true,\n    inert: false,\n};\nRemoveScroll.classNames = {\n    fullWidth: fullWidthClassName,\n    zeroRight: zeroRightClassName,\n};\nexport { RemoveScroll };\n", "export var zeroRightClassName = 'right-scroll-bar-position';\nexport var fullWidthClassName = 'width-before-scroll-bar';\nexport var noScrollbarsClassName = 'with-scroll-bars-hidden';\n/**\n * Name of a CSS variable containing the amount of \"hidden\" scrollbar\n * ! might be undefined ! use will fallback!\n */\nexport var removedBarSizeVariable = '--removed-body-scroll-bar-size';\n", "/**\n * Assigns a value for a given ref, no matter of the ref format\n * @param {RefObject} ref - a callback function or ref object\n * @param value - a new value\n *\n * @see https://github.com/theKashey/use-callback-ref#assignref\n * @example\n * const refObject = useRef();\n * const refFn = (ref) => {....}\n *\n * assignRef(refObject, \"refValue\");\n * assignRef(refFn, \"refValue\");\n */\nexport function assignRef(ref, value) {\n    if (typeof ref === 'function') {\n        ref(value);\n    }\n    else if (ref) {\n        ref.current = value;\n    }\n    return ref;\n}\n", "import { useState } from 'react';\n/**\n * creates a MutableRef with ref change callback\n * @param initialValue - initial ref value\n * @param {Function} callback - a callback to run when value changes\n *\n * @example\n * const ref = useCallbackRef(0, (newValue, oldValue) => console.log(oldValue, '->', newValue);\n * ref.current = 1;\n * // prints 0 -> 1\n *\n * @see https://reactjs.org/docs/hooks-reference.html#useref\n * @see https://github.com/theKashey/use-callback-ref#usecallbackref---to-replace-reactuseref\n * @returns {MutableRefObject}\n */\nexport function useCallbackRef(initialValue, callback) {\n    var ref = useState(function () { return ({\n        // value\n        value: initialValue,\n        // last callback\n        callback: callback,\n        // \"memoized\" public interface\n        facade: {\n            get current() {\n                return ref.value;\n            },\n            set current(value) {\n                var last = ref.value;\n                if (last !== value) {\n                    ref.value = value;\n                    ref.callback(value, last);\n                }\n            },\n        },\n    }); })[0];\n    // update callback\n    ref.callback = callback;\n    return ref.facade;\n}\n", "import * as React from 'react';\nimport { assignRef } from './assignRef';\nimport { useCallbackRef } from './useRef';\nvar useIsomorphicLayoutEffect = typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\nvar currentValues = new WeakMap();\n/**\n * Merges two or more refs together providing a single interface to set their value\n * @param {RefObject|Ref} refs\n * @returns {MutableRefObject} - a new ref, which translates all changes to {refs}\n *\n * @see {@link mergeRefs} a version without buit-in memoization\n * @see https://github.com/theKashey/use-callback-ref#usemergerefs\n * @example\n * const Component = React.forwardRef((props, ref) => {\n *   const ownRef = useRef();\n *   const domRef = useMergeRefs([ref, ownRef]); // 👈 merge together\n *   return <div ref={domRef}>...</div>\n * }\n */\nexport function useMergeRefs(refs, defaultValue) {\n    var callbackRef = useCallbackRef(defaultValue || null, function (newValue) {\n        return refs.forEach(function (ref) { return assignRef(ref, newValue); });\n    });\n    // handle refs changes - added or removed\n    useIsomorphicLayoutEffect(function () {\n        var oldValue = currentValues.get(callbackRef);\n        if (oldValue) {\n            var prevRefs_1 = new Set(oldValue);\n            var nextRefs_1 = new Set(refs);\n            var current_1 = callbackRef.current;\n            prevRefs_1.forEach(function (ref) {\n                if (!nextRefs_1.has(ref)) {\n                    assignRef(ref, null);\n                }\n            });\n            nextRefs_1.forEach(function (ref) {\n                if (!prevRefs_1.has(ref)) {\n                    assignRef(ref, current_1);\n                }\n            });\n        }\n        currentValues.set(callbackRef, refs);\n    }, [refs]);\n    return callbackRef;\n}\n", "import { __assign } from \"tslib\";\nimport * as React from 'react';\nimport { useSidecar } from './hook';\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function sidecar(importer, errorComponent) {\n    var ErrorCase = function () { return errorComponent; };\n    return function Sidecar(props) {\n        var _a = useSidecar(importer, props.sideCar), Car = _a[0], error = _a[1];\n        if (error && errorComponent) {\n            return ErrorCase;\n        }\n        // @ts-expect-error type shenanigans\n        return Car ? React.createElement(Car, __assign({}, props)) : null;\n    };\n}\n", "import { useState, useEffect } from 'react';\nimport { env } from './env';\nvar cache = new WeakMap();\nvar NO_OPTIONS = {};\nexport function useSidecar(importer, effect) {\n    var options = (effect && effect.options) || NO_OPTIONS;\n    if (env.isNode && !options.ssr) {\n        return [null, null];\n    }\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    return useRealSidecar(importer, effect);\n}\nfunction useRealSidecar(importer, effect) {\n    var options = (effect && effect.options) || NO_OPTIONS;\n    var couldUseCache = env.forceCache || (env.isNode && !!options.ssr) || !options.async;\n    var _a = useState(couldUseCache ? function () { return cache.get(importer); } : undefined), Car = _a[0], setCar = _a[1];\n    var _b = useState(null), error = _b[0], setError = _b[1];\n    useEffect(function () {\n        if (!Car) {\n            importer().then(function (car) {\n                var resolved = effect ? effect.read() : car.default || car;\n                if (!resolved) {\n                    console.error('Sidecar error: with importer', importer);\n                    var error_1;\n                    if (effect) {\n                        console.error('Sidecar error: with medium', effect);\n                        error_1 = new Error('Sidecar medium was not found');\n                    }\n                    else {\n                        error_1 = new Error('Sidecar was not found in exports');\n                    }\n                    setError(function () { return error_1; });\n                    throw error_1;\n                }\n                cache.set(importer, resolved);\n                setCar(function () { return resolved; });\n            }, function (e) { return setError(function () { return e; }); });\n        }\n    }, []);\n    return [Car, error];\n}\n", "import { __assign } from \"tslib\";\nfunction ItoI(a) {\n    return a;\n}\nfunction innerCreateMedium(defaults, middleware) {\n    if (middleware === void 0) { middleware = ItoI; }\n    var buffer = [];\n    var assigned = false;\n    var medium = {\n        read: function () {\n            if (assigned) {\n                throw new Error('Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.');\n            }\n            if (buffer.length) {\n                return buffer[buffer.length - 1];\n            }\n            return defaults;\n        },\n        useMedium: function (data) {\n            var item = middleware(data, assigned);\n            buffer.push(item);\n            return function () {\n                buffer = buffer.filter(function (x) { return x !== item; });\n            };\n        },\n        assignSyncMedium: function (cb) {\n            assigned = true;\n            while (buffer.length) {\n                var cbs = buffer;\n                buffer = [];\n                cbs.forEach(cb);\n            }\n            buffer = {\n                push: function (x) { return cb(x); },\n                filter: function () { return buffer; },\n            };\n        },\n        assignMedium: function (cb) {\n            assigned = true;\n            var pendingQueue = [];\n            if (buffer.length) {\n                var cbs = buffer;\n                buffer = [];\n                cbs.forEach(cb);\n                pendingQueue = buffer;\n            }\n            var executeQueue = function () {\n                var cbs = pendingQueue;\n                pendingQueue = [];\n                cbs.forEach(cb);\n            };\n            var cycle = function () { return Promise.resolve().then(executeQueue); };\n            cycle();\n            buffer = {\n                push: function (x) {\n                    pendingQueue.push(x);\n                    cycle();\n                },\n                filter: function (filter) {\n                    pendingQueue = pendingQueue.filter(filter);\n                    return buffer;\n                },\n            };\n        },\n    };\n    return medium;\n}\nexport function createMedium(defaults, middleware) {\n    if (middleware === void 0) { middleware = ItoI; }\n    return innerCreateMedium(defaults, middleware);\n}\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function createSidecarMedium(options) {\n    if (options === void 0) { options = {}; }\n    var medium = innerCreateMedium(null);\n    medium.options = __assign({ async: true, ssr: false }, options);\n    return medium;\n}\n", "import { __assign } from \"tslib\";\nimport * as React from 'react';\nimport { useState, useCallback, useEffect, useLayoutEffect } from 'react';\nexport function renderCar(WrappedComponent, defaults) {\n    function State(_a) {\n        var stateRef = _a.stateRef, props = _a.props;\n        var renderTarget = useCallback(function SideTarget() {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            useLayoutEffect(function () {\n                stateRef.current(args);\n            });\n            return null;\n        }, []);\n        // @ts-ignore\n        return React.createElement(WrappedComponent, __assign({}, props, { children: renderTarget }));\n    }\n    var Children = React.memo(function (_a) {\n        var stateRef = _a.stateRef, defaultState = _a.defaultState, children = _a.children;\n        var _b = useState(defaultState.current), state = _b[0], setState = _b[1];\n        useEffect(function () {\n            stateRef.current = setState;\n        }, []);\n        return children.apply(void 0, state);\n    }, function () { return true; });\n    return function Combiner(props) {\n        var defaultState = React.useRef(defaults(props));\n        var ref = React.useRef(function (state) { return (defaultState.current = state); });\n        return (React.createElement(React.Fragment, null,\n            React.createElement(State, { stateRef: ref, props: props }),\n            React.createElement(Children, { stateRef: ref, defaultState: defaultState, children: props.children })));\n    };\n}\n", "import { __assign, __rest } from \"tslib\";\nimport * as React from 'react';\nvar SideCar = function (_a) {\n    var sideCar = _a.sideCar, rest = __rest(_a, [\"sideCar\"]);\n    if (!sideCar) {\n        throw new Error('Sidecar: please provide `sideCar` property to import the right car');\n    }\n    var Target = sideCar.read();\n    if (!Target) {\n        throw new Error('Sidecar medium not found');\n    }\n    return React.createElement(Target, __assign({}, rest));\n};\nSideCar.isSideCarExport = true;\nexport function exportSidecar(medium, exported) {\n    medium.useMedium(exported);\n    return SideCar;\n}\n", "import { createSidecarMedium } from 'use-sidecar';\nexport var effectCar = createSidecarMedium();\n", "import { __spreadArray } from \"tslib\";\nimport * as React from 'react';\nimport { RemoveScrollBar } from 'react-remove-scroll-bar';\nimport { styleSingleton } from 'react-style-singleton';\nimport { nonPassive } from './aggresiveCapture';\nimport { handleScroll, locationCouldBeScrolled } from './handleScroll';\nexport var getTouchXY = function (event) {\n    return 'changedTouches' in event ? [event.changedTouches[0].clientX, event.changedTouches[0].clientY] : [0, 0];\n};\nexport var getDeltaXY = function (event) { return [event.deltaX, event.deltaY]; };\nvar extractRef = function (ref) {\n    return ref && 'current' in ref ? ref.current : ref;\n};\nvar deltaCompare = function (x, y) { return x[0] === y[0] && x[1] === y[1]; };\nvar generateStyle = function (id) { return \"\\n  .block-interactivity-\".concat(id, \" {pointer-events: none;}\\n  .allow-interactivity-\").concat(id, \" {pointer-events: all;}\\n\"); };\nvar idCounter = 0;\nvar lockStack = [];\nexport function RemoveScrollSideCar(props) {\n    var shouldPreventQueue = React.useRef([]);\n    var touchStartRef = React.useRef([0, 0]);\n    var activeAxis = React.useRef();\n    var id = React.useState(idCounter++)[0];\n    var Style = React.useState(styleSingleton)[0];\n    var lastProps = React.useRef(props);\n    React.useEffect(function () {\n        lastProps.current = props;\n    }, [props]);\n    React.useEffect(function () {\n        if (props.inert) {\n            document.body.classList.add(\"block-interactivity-\".concat(id));\n            var allow_1 = __spreadArray([props.lockRef.current], (props.shards || []).map(extractRef), true).filter(Boolean);\n            allow_1.forEach(function (el) { return el.classList.add(\"allow-interactivity-\".concat(id)); });\n            return function () {\n                document.body.classList.remove(\"block-interactivity-\".concat(id));\n                allow_1.forEach(function (el) { return el.classList.remove(\"allow-interactivity-\".concat(id)); });\n            };\n        }\n        return;\n    }, [props.inert, props.lockRef.current, props.shards]);\n    var shouldCancelEvent = React.useCallback(function (event, parent) {\n        if (('touches' in event && event.touches.length === 2) || (event.type === 'wheel' && event.ctrlKey)) {\n            return !lastProps.current.allowPinchZoom;\n        }\n        var touch = getTouchXY(event);\n        var touchStart = touchStartRef.current;\n        var deltaX = 'deltaX' in event ? event.deltaX : touchStart[0] - touch[0];\n        var deltaY = 'deltaY' in event ? event.deltaY : touchStart[1] - touch[1];\n        var currentAxis;\n        var target = event.target;\n        var moveDirection = Math.abs(deltaX) > Math.abs(deltaY) ? 'h' : 'v';\n        // allow horizontal touch move on Range inputs. They will not cause any scroll\n        if ('touches' in event && moveDirection === 'h' && target.type === 'range') {\n            return false;\n        }\n        var canBeScrolledInMainDirection = locationCouldBeScrolled(moveDirection, target);\n        if (!canBeScrolledInMainDirection) {\n            return true;\n        }\n        if (canBeScrolledInMainDirection) {\n            currentAxis = moveDirection;\n        }\n        else {\n            currentAxis = moveDirection === 'v' ? 'h' : 'v';\n            canBeScrolledInMainDirection = locationCouldBeScrolled(moveDirection, target);\n            // other axis might be not scrollable\n        }\n        if (!canBeScrolledInMainDirection) {\n            return false;\n        }\n        if (!activeAxis.current && 'changedTouches' in event && (deltaX || deltaY)) {\n            activeAxis.current = currentAxis;\n        }\n        if (!currentAxis) {\n            return true;\n        }\n        var cancelingAxis = activeAxis.current || currentAxis;\n        return handleScroll(cancelingAxis, parent, event, cancelingAxis === 'h' ? deltaX : deltaY, true);\n    }, []);\n    var shouldPrevent = React.useCallback(function (_event) {\n        var event = _event;\n        if (!lockStack.length || lockStack[lockStack.length - 1] !== Style) {\n            // not the last active\n            return;\n        }\n        var delta = 'deltaY' in event ? getDeltaXY(event) : getTouchXY(event);\n        var sourceEvent = shouldPreventQueue.current.filter(function (e) { return e.name === event.type && (e.target === event.target || event.target === e.shadowParent) && deltaCompare(e.delta, delta); })[0];\n        // self event, and should be canceled\n        if (sourceEvent && sourceEvent.should) {\n            if (event.cancelable) {\n                event.preventDefault();\n            }\n            return;\n        }\n        // outside or shard event\n        if (!sourceEvent) {\n            var shardNodes = (lastProps.current.shards || [])\n                .map(extractRef)\n                .filter(Boolean)\n                .filter(function (node) { return node.contains(event.target); });\n            var shouldStop = shardNodes.length > 0 ? shouldCancelEvent(event, shardNodes[0]) : !lastProps.current.noIsolation;\n            if (shouldStop) {\n                if (event.cancelable) {\n                    event.preventDefault();\n                }\n            }\n        }\n    }, []);\n    var shouldCancel = React.useCallback(function (name, delta, target, should) {\n        var event = { name: name, delta: delta, target: target, should: should, shadowParent: getOutermostShadowParent(target) };\n        shouldPreventQueue.current.push(event);\n        setTimeout(function () {\n            shouldPreventQueue.current = shouldPreventQueue.current.filter(function (e) { return e !== event; });\n        }, 1);\n    }, []);\n    var scrollTouchStart = React.useCallback(function (event) {\n        touchStartRef.current = getTouchXY(event);\n        activeAxis.current = undefined;\n    }, []);\n    var scrollWheel = React.useCallback(function (event) {\n        shouldCancel(event.type, getDeltaXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n    }, []);\n    var scrollTouchMove = React.useCallback(function (event) {\n        shouldCancel(event.type, getTouchXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n    }, []);\n    React.useEffect(function () {\n        lockStack.push(Style);\n        props.setCallbacks({\n            onScrollCapture: scrollWheel,\n            onWheelCapture: scrollWheel,\n            onTouchMoveCapture: scrollTouchMove,\n        });\n        document.addEventListener('wheel', shouldPrevent, nonPassive);\n        document.addEventListener('touchmove', shouldPrevent, nonPassive);\n        document.addEventListener('touchstart', scrollTouchStart, nonPassive);\n        return function () {\n            lockStack = lockStack.filter(function (inst) { return inst !== Style; });\n            document.removeEventListener('wheel', shouldPrevent, nonPassive);\n            document.removeEventListener('touchmove', shouldPrevent, nonPassive);\n            document.removeEventListener('touchstart', scrollTouchStart, nonPassive);\n        };\n    }, []);\n    var removeScrollBar = props.removeScrollBar, inert = props.inert;\n    return (React.createElement(React.Fragment, null,\n        inert ? React.createElement(Style, { styles: generateStyle(id) }) : null,\n        removeScrollBar ? React.createElement(RemoveScrollBar, { noRelative: props.noRelative, gapMode: props.gapMode }) : null));\n}\nfunction getOutermostShadowParent(node) {\n    var shadowParent = null;\n    while (node !== null) {\n        if (node instanceof ShadowRoot) {\n            shadowParent = node.host;\n            node = node.host;\n        }\n        node = node.parentNode;\n    }\n    return shadowParent;\n}\n", "import * as React from 'react';\nimport { styleSingleton } from 'react-style-singleton';\nimport { fullWidthClassName, zeroRightClassName, noScrollbarsClassName, removedBarSizeVariable } from './constants';\nimport { getGapWidth } from './utils';\nvar Style = styleSingleton();\nexport var lockAttribute = 'data-scroll-locked';\n// important tip - once we measure scrollBar width and remove them\n// we could not repeat this operation\n// thus we are using style-singleton - only the first \"yet correct\" style will be applied.\nvar getStyles = function (_a, allowRelative, gapMode, important) {\n    var left = _a.left, top = _a.top, right = _a.right, gap = _a.gap;\n    if (gapMode === void 0) { gapMode = 'margin'; }\n    return \"\\n  .\".concat(noScrollbarsClassName, \" {\\n   overflow: hidden \").concat(important, \";\\n   padding-right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  body[\").concat(lockAttribute, \"] {\\n    overflow: hidden \").concat(important, \";\\n    overscroll-behavior: contain;\\n    \").concat([\n        allowRelative && \"position: relative \".concat(important, \";\"),\n        gapMode === 'margin' &&\n            \"\\n    padding-left: \".concat(left, \"px;\\n    padding-top: \").concat(top, \"px;\\n    padding-right: \").concat(right, \"px;\\n    margin-left:0;\\n    margin-top:0;\\n    margin-right: \").concat(gap, \"px \").concat(important, \";\\n    \"),\n        gapMode === 'padding' && \"padding-right: \".concat(gap, \"px \").concat(important, \";\"),\n    ]\n        .filter(Boolean)\n        .join(''), \"\\n  }\\n  \\n  .\").concat(zeroRightClassName, \" {\\n    right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  \\n  .\").concat(fullWidthClassName, \" {\\n    margin-right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  \\n  .\").concat(zeroRightClassName, \" .\").concat(zeroRightClassName, \" {\\n    right: 0 \").concat(important, \";\\n  }\\n  \\n  .\").concat(fullWidthClassName, \" .\").concat(fullWidthClassName, \" {\\n    margin-right: 0 \").concat(important, \";\\n  }\\n  \\n  body[\").concat(lockAttribute, \"] {\\n    \").concat(removedBarSizeVariable, \": \").concat(gap, \"px;\\n  }\\n\");\n};\nvar getCurrentUseCounter = function () {\n    var counter = parseInt(document.body.getAttribute(lockAttribute) || '0', 10);\n    return isFinite(counter) ? counter : 0;\n};\nexport var useLockAttribute = function () {\n    React.useEffect(function () {\n        document.body.setAttribute(lockAttribute, (getCurrentUseCounter() + 1).toString());\n        return function () {\n            var newCounter = getCurrentUseCounter() - 1;\n            if (newCounter <= 0) {\n                document.body.removeAttribute(lockAttribute);\n            }\n            else {\n                document.body.setAttribute(lockAttribute, newCounter.toString());\n            }\n        };\n    }, []);\n};\n/**\n * Removes page scrollbar and blocks page scroll when mounted\n */\nexport var RemoveScrollBar = function (_a) {\n    var noRelative = _a.noRelative, noImportant = _a.noImportant, _b = _a.gapMode, gapMode = _b === void 0 ? 'margin' : _b;\n    useLockAttribute();\n    /*\n     gap will be measured on every component mount\n     however it will be used only by the \"first\" invocation\n     due to singleton nature of <Style\n     */\n    var gap = React.useMemo(function () { return getGapWidth(gapMode); }, [gapMode]);\n    return React.createElement(Style, { styles: getStyles(gap, !noRelative, gapMode, !noImportant ? '!important' : '') });\n};\n", "import * as React from 'react';\nimport { stylesheetSingleton } from './singleton';\n/**\n * creates a hook to control style singleton\n * @see {@link styleSingleton} for a safer component version\n * @example\n * ```tsx\n * const useStyle = styleHookSingleton();\n * ///\n * useStyle('body { overflow: hidden}');\n */\nexport var styleHookSingleton = function () {\n    var sheet = stylesheetSingleton();\n    return function (styles, isDynamic) {\n        React.useEffect(function () {\n            sheet.add(styles);\n            return function () {\n                sheet.remove();\n            };\n        }, [styles && isDynamic]);\n    };\n};\n", "var currentNonce;\nexport var setNonce = function (nonce) {\n    currentNonce = nonce;\n};\nexport var getNonce = function () {\n    if (currentNonce) {\n        return currentNonce;\n    }\n    if (typeof __webpack_nonce__ !== 'undefined') {\n        return __webpack_nonce__;\n    }\n    return undefined;\n};\n", "import { getNonce } from 'get-nonce';\nfunction makeStyleTag() {\n    if (!document)\n        return null;\n    var tag = document.createElement('style');\n    tag.type = 'text/css';\n    var nonce = getNonce();\n    if (nonce) {\n        tag.setAttribute('nonce', nonce);\n    }\n    return tag;\n}\nfunction injectStyles(tag, css) {\n    // @ts-ignore\n    if (tag.styleSheet) {\n        // @ts-ignore\n        tag.styleSheet.cssText = css;\n    }\n    else {\n        tag.appendChild(document.createTextNode(css));\n    }\n}\nfunction insertStyleTag(tag) {\n    var head = document.head || document.getElementsByTagName('head')[0];\n    head.appendChild(tag);\n}\nexport var stylesheetSingleton = function () {\n    var counter = 0;\n    var stylesheet = null;\n    return {\n        add: function (style) {\n            if (counter == 0) {\n                if ((stylesheet = makeStyleTag())) {\n                    injectStyles(stylesheet, style);\n                    insertStyleTag(stylesheet);\n                }\n            }\n            counter++;\n        },\n        remove: function () {\n            counter--;\n            if (!counter && stylesheet) {\n                stylesheet.parentNode && stylesheet.parentNode.removeChild(stylesheet);\n                stylesheet = null;\n            }\n        },\n    };\n};\n", "import { styleHook<PERSON>ingleton } from './hook';\n/**\n * create a Component to add styles on demand\n * - styles are added when first instance is mounted\n * - styles are removed when the last instance is unmounted\n * - changing styles in runtime does nothing unless dynamic is set. But with multiple components that can lead to the undefined behavior\n */\nexport var styleSingleton = function () {\n    var useStyle = styleHookSingleton();\n    var Sheet = function (_a) {\n        var styles = _a.styles, dynamic = _a.dynamic;\n        useStyle(styles, dynamic);\n        return null;\n    };\n    return Sheet;\n};\n", "export var zeroGap = {\n    left: 0,\n    top: 0,\n    right: 0,\n    gap: 0,\n};\nvar parse = function (x) { return parseInt(x || '', 10) || 0; };\nvar getOffset = function (gapMode) {\n    var cs = window.getComputedStyle(document.body);\n    var left = cs[gapMode === 'padding' ? 'paddingLeft' : 'marginLeft'];\n    var top = cs[gapMode === 'padding' ? 'paddingTop' : 'marginTop'];\n    var right = cs[gapMode === 'padding' ? 'paddingRight' : 'marginRight'];\n    return [parse(left), parse(top), parse(right)];\n};\nexport var getGapWidth = function (gapMode) {\n    if (gapMode === void 0) { gapMode = 'margin'; }\n    if (typeof window === 'undefined') {\n        return zeroGap;\n    }\n    var offsets = getOffset(gapMode);\n    var documentWidth = document.documentElement.clientWidth;\n    var windowWidth = window.innerWidth;\n    return {\n        left: offsets[0],\n        top: offsets[1],\n        right: offsets[2],\n        gap: Math.max(0, windowWidth - documentWidth + offsets[2] - offsets[0]),\n    };\n};\n", "var passiveSupported = false;\nif (typeof window !== 'undefined') {\n    try {\n        var options = Object.defineProperty({}, 'passive', {\n            get: function () {\n                passiveSupported = true;\n                return true;\n            },\n        });\n        // @ts-ignore\n        window.addEventListener('test', options, options);\n        // @ts-ignore\n        window.removeEventListener('test', options, options);\n    }\n    catch (err) {\n        passiveSupported = false;\n    }\n}\nexport var nonPassive = passiveSupported ? { passive: false } : false;\n", "var alwaysContainsScroll = function (node) {\n    // textarea will always _contain_ scroll inside self. It only can be hidden\n    return node.tagName === 'TEXTAREA';\n};\nvar elementCanBeScrolled = function (node, overflow) {\n    if (!(node instanceof Element)) {\n        return false;\n    }\n    var styles = window.getComputedStyle(node);\n    return (\n    // not-not-scrollable\n    styles[overflow] !== 'hidden' &&\n        // contains scroll inside self\n        !(styles.overflowY === styles.overflowX && !alwaysContainsScroll(node) && styles[overflow] === 'visible'));\n};\nvar elementCouldBeVScrolled = function (node) { return elementCanBeScrolled(node, 'overflowY'); };\nvar elementCouldBeHScrolled = function (node) { return elementCanBeScrolled(node, 'overflowX'); };\nexport var locationCouldBeScrolled = function (axis, node) {\n    var ownerDocument = node.ownerDocument;\n    var current = node;\n    do {\n        // Skip over shadow root\n        if (typeof ShadowRoot !== 'undefined' && current instanceof ShadowRoot) {\n            current = current.host;\n        }\n        var isScrollable = elementCouldBeScrolled(axis, current);\n        if (isScrollable) {\n            var _a = getScrollVariables(axis, current), scrollHeight = _a[1], clientHeight = _a[2];\n            if (scrollHeight > clientHeight) {\n                return true;\n            }\n        }\n        current = current.parentNode;\n    } while (current && current !== ownerDocument.body);\n    return false;\n};\nvar getVScrollVariables = function (_a) {\n    var scrollTop = _a.scrollTop, scrollHeight = _a.scrollHeight, clientHeight = _a.clientHeight;\n    return [\n        scrollTop,\n        scrollHeight,\n        clientHeight,\n    ];\n};\nvar getHScrollVariables = function (_a) {\n    var scrollLeft = _a.scrollLeft, scrollWidth = _a.scrollWidth, clientWidth = _a.clientWidth;\n    return [\n        scrollLeft,\n        scrollWidth,\n        clientWidth,\n    ];\n};\nvar elementCouldBeScrolled = function (axis, node) {\n    return axis === 'v' ? elementCouldBeVScrolled(node) : elementCouldBeHScrolled(node);\n};\nvar getScrollVariables = function (axis, node) {\n    return axis === 'v' ? getVScrollVariables(node) : getHScrollVariables(node);\n};\nvar getDirectionFactor = function (axis, direction) {\n    /**\n     * If the element's direction is rtl (right-to-left), then scrollLeft is 0 when the scrollbar is at its rightmost position,\n     * and then increasingly negative as you scroll towards the end of the content.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/Element/scrollLeft\n     */\n    return axis === 'h' && direction === 'rtl' ? -1 : 1;\n};\nexport var handleScroll = function (axis, endTarget, event, sourceDelta, noOverscroll) {\n    var directionFactor = getDirectionFactor(axis, window.getComputedStyle(endTarget).direction);\n    var delta = directionFactor * sourceDelta;\n    // find scrollable target\n    var target = event.target;\n    var targetInLock = endTarget.contains(target);\n    var shouldCancelScroll = false;\n    var isDeltaPositive = delta > 0;\n    var availableScroll = 0;\n    var availableScrollTop = 0;\n    do {\n        if (!target) {\n            break;\n        }\n        var _a = getScrollVariables(axis, target), position = _a[0], scroll_1 = _a[1], capacity = _a[2];\n        var elementScroll = scroll_1 - capacity - directionFactor * position;\n        if (position || elementScroll) {\n            if (elementCouldBeScrolled(axis, target)) {\n                availableScroll += elementScroll;\n                availableScrollTop += position;\n            }\n        }\n        var parent_1 = target.parentNode;\n        // we will \"bubble\" from ShadowDom in case we are, or just to the parent in normal case\n        // this is the same logic used in focus-lock\n        target = (parent_1 && parent_1.nodeType === Node.DOCUMENT_FRAGMENT_NODE ? parent_1.host : parent_1);\n    } while (\n    // portaled content\n    (!targetInLock && target !== document.body) ||\n        // self content\n        (targetInLock && (endTarget.contains(target) || endTarget === target)));\n    // handle epsilon around 0 (non standard zoom levels)\n    if (isDeltaPositive &&\n        ((noOverscroll && Math.abs(availableScroll) < 1) || (!noOverscroll && delta > availableScroll))) {\n        shouldCancelScroll = true;\n    }\n    else if (!isDeltaPositive &&\n        ((noOverscroll && Math.abs(availableScrollTop) < 1) || (!noOverscroll && -delta > availableScrollTop))) {\n        shouldCancelScroll = true;\n    }\n    return shouldCancelScroll;\n};\n", "import { exportSidecar } from 'use-sidecar';\nimport { RemoveScrollSideCar } from './SideEffect';\nimport { effectCar } from './medium';\nexport default exportSidecar(effectCar, RemoveScrollSideCar);\n", "var getDefaultParent = function (originalTarget) {\n    if (typeof document === 'undefined') {\n        return null;\n    }\n    var sampleTarget = Array.isArray(originalTarget) ? originalTarget[0] : originalTarget;\n    return sampleTarget.ownerDocument.body;\n};\nvar counterMap = new WeakMap();\nvar uncontrolledNodes = new WeakMap();\nvar markerMap = {};\nvar lockCount = 0;\nvar unwrapHost = function (node) {\n    return node && (node.host || unwrapHost(node.parentNode));\n};\nvar correctTargets = function (parent, targets) {\n    return targets\n        .map(function (target) {\n        if (parent.contains(target)) {\n            return target;\n        }\n        var correctedTarget = unwrapHost(target);\n        if (correctedTarget && parent.contains(correctedTarget)) {\n            return correctedTarget;\n        }\n        console.error('aria-hidden', target, 'in not contained inside', parent, '. Doing nothing');\n        return null;\n    })\n        .filter(function (x) { return Boolean(x); });\n};\n/**\n * Marks everything except given node(or nodes) as aria-hidden\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @param {String} [controlAttribute] - html Attribute to control\n * @return {Undo} undo command\n */\nvar applyAttributeToOthers = function (originalTarget, parentNode, markerName, controlAttribute) {\n    var targets = correctTargets(parentNode, Array.isArray(originalTarget) ? originalTarget : [originalTarget]);\n    if (!markerMap[markerName]) {\n        markerMap[markerName] = new WeakMap();\n    }\n    var markerCounter = markerMap[markerName];\n    var hiddenNodes = [];\n    var elementsToKeep = new Set();\n    var elementsToStop = new Set(targets);\n    var keep = function (el) {\n        if (!el || elementsToKeep.has(el)) {\n            return;\n        }\n        elementsToKeep.add(el);\n        keep(el.parentNode);\n    };\n    targets.forEach(keep);\n    var deep = function (parent) {\n        if (!parent || elementsToStop.has(parent)) {\n            return;\n        }\n        Array.prototype.forEach.call(parent.children, function (node) {\n            if (elementsToKeep.has(node)) {\n                deep(node);\n            }\n            else {\n                try {\n                    var attr = node.getAttribute(controlAttribute);\n                    var alreadyHidden = attr !== null && attr !== 'false';\n                    var counterValue = (counterMap.get(node) || 0) + 1;\n                    var markerValue = (markerCounter.get(node) || 0) + 1;\n                    counterMap.set(node, counterValue);\n                    markerCounter.set(node, markerValue);\n                    hiddenNodes.push(node);\n                    if (counterValue === 1 && alreadyHidden) {\n                        uncontrolledNodes.set(node, true);\n                    }\n                    if (markerValue === 1) {\n                        node.setAttribute(markerName, 'true');\n                    }\n                    if (!alreadyHidden) {\n                        node.setAttribute(controlAttribute, 'true');\n                    }\n                }\n                catch (e) {\n                    console.error('aria-hidden: cannot operate on ', node, e);\n                }\n            }\n        });\n    };\n    deep(parentNode);\n    elementsToKeep.clear();\n    lockCount++;\n    return function () {\n        hiddenNodes.forEach(function (node) {\n            var counterValue = counterMap.get(node) - 1;\n            var markerValue = markerCounter.get(node) - 1;\n            counterMap.set(node, counterValue);\n            markerCounter.set(node, markerValue);\n            if (!counterValue) {\n                if (!uncontrolledNodes.has(node)) {\n                    node.removeAttribute(controlAttribute);\n                }\n                uncontrolledNodes.delete(node);\n            }\n            if (!markerValue) {\n                node.removeAttribute(markerName);\n            }\n        });\n        lockCount--;\n        if (!lockCount) {\n            // clear\n            counterMap = new WeakMap();\n            counterMap = new WeakMap();\n            uncontrolledNodes = new WeakMap();\n            markerMap = {};\n        }\n    };\n};\n/**\n * Marks everything except given node(or nodes) as aria-hidden\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nexport var hideOthers = function (originalTarget, parentNode, markerName) {\n    if (markerName === void 0) { markerName = 'data-aria-hidden'; }\n    var targets = Array.from(Array.isArray(originalTarget) ? originalTarget : [originalTarget]);\n    var activeParentNode = parentNode || getDefaultParent(originalTarget);\n    if (!activeParentNode) {\n        return function () { return null; };\n    }\n    // we should not hide aria-live elements - https://github.com/theKashey/aria-hidden/issues/10\n    // and script elements, as they have no impact on accessibility.\n    targets.push.apply(targets, Array.from(activeParentNode.querySelectorAll('[aria-live], script')));\n    return applyAttributeToOthers(targets, activeParentNode, markerName, 'aria-hidden');\n};\n/**\n * Marks everything except given node(or nodes) as inert\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nexport var inertOthers = function (originalTarget, parentNode, markerName) {\n    if (markerName === void 0) { markerName = 'data-inert-ed'; }\n    var activeParentNode = parentNode || getDefaultParent(originalTarget);\n    if (!activeParentNode) {\n        return function () { return null; };\n    }\n    return applyAttributeToOthers(originalTarget, activeParentNode, markerName, 'inert');\n};\n/**\n * @returns if current browser supports inert\n */\nexport var supportsInert = function () {\n    return typeof HTMLElement !== 'undefined' && HTMLElement.prototype.hasOwnProperty('inert');\n};\n/**\n * Automatic function to \"suppress\" DOM elements - _hide_ or _inert_ in the best possible way\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nexport var suppressOthers = function (originalTarget, parentNode, markerName) {\n    if (markerName === void 0) { markerName = 'data-suppressed'; }\n    return (supportsInert() ? inertOthers : hideOthers)(originalTarget, parentNode, markerName);\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,YAAuB;AA2MnB,yBAAA;AAtMJ,IAAM,qBAAqB;AAC3B,IAAM,uBAAuB;AAC7B,IAAM,gBAAgB,EAAE,SAAS,OAAO,YAAY,KAAK;AAQzD,IAAM,mBAAmB;AAgCzB,IAAM,aAAmB,iBAA+C,CAAC,OAAO,iBAAiB;AAC/F,QAAM;IACJ,OAAO;IACP,UAAU;IACV,kBAAkB;IAClB,oBAAoB;IACpB,GAAG;EACL,IAAI;AACJ,QAAM,CAAC,WAAW,YAAY,IAAU,eAA6B,IAAI;AACzE,QAAM,mBAAmB,eAAe,oBAAoB;AAC5D,QAAM,qBAAqB,eAAe,sBAAsB;AAChE,QAAM,wBAA8B,aAA2B,IAAI;AACnE,QAAM,eAAe,gBAAgB,cAAc,CAAC,SAAS,aAAa,IAAI,CAAC;AAE/E,QAAM,aAAmB,aAAO;IAC9B,QAAQ;IACR,QAAQ;AACN,WAAK,SAAS;IAChB;IACA,SAAS;AACP,WAAK,SAAS;IAChB;EACF,CAAC,EAAE;AAGG,EAAA,gBAAU,MAAM;AACpB,QAAI,SAAS;AACX,UAASA,iBAAT,SAAuB,OAAmB;AACxC,YAAI,WAAW,UAAU,CAAC,UAAW;AACrC,cAAM,SAAS,MAAM;AACrB,YAAI,UAAU,SAAS,MAAM,GAAG;AAC9B,gCAAsB,UAAU;QAClC,OAAO;AACL,gBAAM,sBAAsB,SAAS,EAAE,QAAQ,KAAK,CAAC;QACvD;MACF,GAESC,kBAAT,SAAwB,OAAmB;AACzC,YAAI,WAAW,UAAU,CAAC,UAAW;AACrC,cAAM,gBAAgB,MAAM;AAY5B,YAAI,kBAAkB,KAAM;AAI5B,YAAI,CAAC,UAAU,SAAS,aAAa,GAAG;AACtC,gBAAM,sBAAsB,SAAS,EAAE,QAAQ,KAAK,CAAC;QACvD;MACF,GAKSC,mBAAT,SAAyB,WAA6B;AACpD,cAAM,iBAAiB,SAAS;AAChC,YAAI,mBAAmB,SAAS,KAAM;AACtC,mBAAW,YAAY,WAAW;AAChC,cAAI,SAAS,aAAa,SAAS,EAAG,OAAM,SAAS;QACvD;MACF;AA1CS,UAAA,gBAAAF,gBAUA,iBAAAC,iBA0BA,kBAAAC;AAQT,eAAS,iBAAiB,WAAWF,cAAa;AAClD,eAAS,iBAAiB,YAAYC,eAAc;AACpD,YAAM,mBAAmB,IAAI,iBAAiBC,gBAAe;AAC7D,UAAI,UAAW,kBAAiB,QAAQ,WAAW,EAAE,WAAW,MAAM,SAAS,KAAK,CAAC;AAErF,aAAO,MAAM;AACX,iBAAS,oBAAoB,WAAWF,cAAa;AACrD,iBAAS,oBAAoB,YAAYC,eAAc;AACvD,yBAAiB,WAAW;MAC9B;IACF;EACF,GAAG,CAAC,SAAS,WAAW,WAAW,MAAM,CAAC;AAEpC,EAAA,gBAAU,MAAM;AACpB,QAAI,WAAW;AACb,uBAAiB,IAAI,UAAU;AAC/B,YAAM,2BAA2B,SAAS;AAC1C,YAAM,sBAAsB,UAAU,SAAS,wBAAwB;AAEvE,UAAI,CAAC,qBAAqB;AACxB,cAAM,aAAa,IAAI,YAAY,oBAAoB,aAAa;AACpE,kBAAU,iBAAiB,oBAAoB,gBAAgB;AAC/D,kBAAU,cAAc,UAAU;AAClC,YAAI,CAAC,WAAW,kBAAkB;AAChC,qBAAW,YAAY,sBAAsB,SAAS,CAAC,GAAG,EAAE,QAAQ,KAAK,CAAC;AAC1E,cAAI,SAAS,kBAAkB,0BAA0B;AACvD,kBAAM,SAAS;UACjB;QACF;MACF;AAEA,aAAO,MAAM;AACX,kBAAU,oBAAoB,oBAAoB,gBAAgB;AAKlE,mBAAW,MAAM;AACf,gBAAM,eAAe,IAAI,YAAY,sBAAsB,aAAa;AACxE,oBAAU,iBAAiB,sBAAsB,kBAAkB;AACnE,oBAAU,cAAc,YAAY;AACpC,cAAI,CAAC,aAAa,kBAAkB;AAClC,kBAAM,4BAA4B,SAAS,MAAM,EAAE,QAAQ,KAAK,CAAC;UACnE;AAEA,oBAAU,oBAAoB,sBAAsB,kBAAkB;AAEtE,2BAAiB,OAAO,UAAU;QACpC,GAAG,CAAC;MACN;IACF;EACF,GAAG,CAAC,WAAW,kBAAkB,oBAAoB,UAAU,CAAC;AAGhE,QAAM,gBAAsB;IAC1B,CAAC,UAA+B;AAC9B,UAAI,CAAC,QAAQ,CAAC,QAAS;AACvB,UAAI,WAAW,OAAQ;AAEvB,YAAM,WAAW,MAAM,QAAQ,SAAS,CAAC,MAAM,UAAU,CAAC,MAAM,WAAW,CAAC,MAAM;AAClF,YAAM,iBAAiB,SAAS;AAEhC,UAAI,YAAY,gBAAgB;AAC9B,cAAME,aAAY,MAAM;AACxB,cAAM,CAAC,OAAO,IAAI,IAAI,iBAAiBA,UAAS;AAChD,cAAM,4BAA4B,SAAS;AAG3C,YAAI,CAAC,2BAA2B;AAC9B,cAAI,mBAAmBA,WAAW,OAAM,eAAe;QACzD,OAAO;AACL,cAAI,CAAC,MAAM,YAAY,mBAAmB,MAAM;AAC9C,kBAAM,eAAe;AACrB,gBAAI,KAAM,OAAM,OAAO,EAAE,QAAQ,KAAK,CAAC;UACzC,WAAW,MAAM,YAAY,mBAAmB,OAAO;AACrD,kBAAM,eAAe;AACrB,gBAAI,KAAM,OAAM,MAAM,EAAE,QAAQ,KAAK,CAAC;UACxC;QACF;MACF;IACF;IACA,CAAC,MAAM,SAAS,WAAW,MAAM;EACnC;AAEA,aACE,wBAAC,UAAU,KAAV,EAAc,UAAU,IAAK,GAAG,YAAY,KAAK,cAAc,WAAW,cAAA,CAAe;AAE9F,CAAC;AAED,WAAW,cAAc;AAUzB,SAAS,WAAW,YAA2B,EAAE,SAAS,MAAM,IAAI,CAAC,GAAG;AACtE,QAAM,2BAA2B,SAAS;AAC1C,aAAW,aAAa,YAAY;AAClC,UAAM,WAAW,EAAE,OAAO,CAAC;AAC3B,QAAI,SAAS,kBAAkB,yBAA0B;EAC3D;AACF;AAKA,SAAS,iBAAiB,WAAwB;AAChD,QAAM,aAAa,sBAAsB,SAAS;AAClD,QAAM,QAAQ,YAAY,YAAY,SAAS;AAC/C,QAAM,OAAO,YAAY,WAAW,QAAQ,GAAG,SAAS;AACxD,SAAO,CAAC,OAAO,IAAI;AACrB;AAYA,SAAS,sBAAsB,WAAwB;AACrD,QAAM,QAAuB,CAAC;AAC9B,QAAM,SAAS,SAAS,iBAAiB,WAAW,WAAW,cAAc;IAC3E,YAAY,CAAC,SAAc;AACzB,YAAM,gBAAgB,KAAK,YAAY,WAAW,KAAK,SAAS;AAChE,UAAI,KAAK,YAAY,KAAK,UAAU,cAAe,QAAO,WAAW;AAIrE,aAAO,KAAK,YAAY,IAAI,WAAW,gBAAgB,WAAW;IACpE;EACF,CAAC;AACD,SAAO,OAAO,SAAS,EAAG,OAAM,KAAK,OAAO,WAA0B;AAGtE,SAAO;AACT;AAMA,SAAS,YAAY,UAAyB,WAAwB;AACpE,aAAW,WAAW,UAAU;AAE9B,QAAI,CAAC,SAAS,SAAS,EAAE,MAAM,UAAU,CAAC,EAAG,QAAO;EACtD;AACF;AAEA,SAAS,SAAS,MAAmB,EAAE,KAAK,GAA2B;AACrE,MAAI,iBAAiB,IAAI,EAAE,eAAe,SAAU,QAAO;AAC3D,SAAO,MAAM;AAEX,QAAI,SAAS,UAAa,SAAS,KAAM,QAAO;AAChD,QAAI,iBAAiB,IAAI,EAAE,YAAY,OAAQ,QAAO;AACtD,WAAO,KAAK;EACd;AACA,SAAO;AACT;AAEA,SAAS,kBAAkB,SAAmE;AAC5F,SAAO,mBAAmB,oBAAoB,YAAY;AAC5D;AAEA,SAAS,MAAM,SAAkC,EAAE,SAAS,MAAM,IAAI,CAAC,GAAG;AAExE,MAAI,WAAW,QAAQ,OAAO;AAC5B,UAAM,2BAA2B,SAAS;AAE1C,YAAQ,MAAM,EAAE,eAAe,KAAK,CAAC;AAErC,QAAI,YAAY,4BAA4B,kBAAkB,OAAO,KAAK;AACxE,cAAQ,OAAO;EACnB;AACF;AAOA,IAAM,mBAAmB,uBAAuB;AAEhD,SAAS,yBAAyB;AAEhC,MAAI,QAAyB,CAAC;AAE9B,SAAO;IACL,IAAI,YAA2B;AAE7B,YAAM,mBAAmB,MAAM,CAAC;AAChC,UAAI,eAAe,kBAAkB;AACnC,6DAAkB;MACpB;AAEA,cAAQ,YAAY,OAAO,UAAU;AACrC,YAAM,QAAQ,UAAU;IAC1B;IAEA,OAAO,YAA2B;;AAChC,cAAQ,YAAY,OAAO,UAAU;AACrC,kBAAM,CAAC,MAAP,mBAAU;IACZ;EACF;AACF;AAEA,SAAS,YAAe,OAAY,MAAS;AAC3C,QAAM,eAAe,CAAC,GAAG,KAAK;AAC9B,QAAM,QAAQ,aAAa,QAAQ,IAAI;AACvC,MAAI,UAAU,IAAI;AAChB,iBAAa,OAAO,OAAO,CAAC;EAC9B;AACA,SAAO;AACT;AAEA,SAAS,YAAY,OAAsB;AACzC,SAAO,MAAM,OAAO,CAAC,SAAS,KAAK,YAAY,GAAG;AACpD;;;ACtVA,IAAAC,SAAuB;AAGvB,IAAI,QAAQ;AAWZ,SAAS,iBAAiB;AAClB,EAAA,iBAAU,MAAM;AACpB,UAAM,aAAa,SAAS,iBAAiB,0BAA0B;AACvE,aAAS,KAAK,sBAAsB,cAAc,WAAW,CAAC,KAAK,iBAAiB,CAAC;AACrF,aAAS,KAAK,sBAAsB,aAAa,WAAW,CAAC,KAAK,iBAAiB,CAAC;AACpF;AAEA,WAAO,MAAM;AACX,UAAI,UAAU,GAAG;AACf,iBAAS,iBAAiB,0BAA0B,EAAE,QAAQ,CAAC,SAAS,KAAK,OAAO,CAAC;MACvF;AACA;IACF;EACF,GAAG,CAAC,CAAC;AACP;AAEA,SAAS,mBAAmB;AAC1B,QAAM,UAAU,SAAS,cAAc,MAAM;AAC7C,UAAQ,aAAa,0BAA0B,EAAE;AACjD,UAAQ,WAAW;AACnB,UAAQ,MAAM,UAAU;AACxB,UAAQ,MAAM,UAAU;AACxB,UAAQ,MAAM,WAAW;AACzB,UAAQ,MAAM,gBAAgB;AAC9B,SAAO;AACT;;;ACRO,IAAI,WAAW,WAAW;AAC/B,aAAW,OAAO,UAAU,SAASC,UAAS,GAAG;AAC7C,aAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,UAAI,UAAU,CAAC;AACf,eAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAC/E;AACA,WAAO;AAAA,EACX;AACA,SAAO,SAAS,MAAM,MAAM,SAAS;AACvC;AAEO,SAAS,OAAO,GAAG,GAAG;AAC3B,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI;AAC9E,MAAE,CAAC,IAAI,EAAE,CAAC;AACd,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B;AACrD,aAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AACpE,UAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC;AACzE,UAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,IACxB;AACJ,SAAO;AACT;AAiKO,SAAS,cAAc,IAAI,MAAM,MAAM;AAC5C,MAAI,QAAQ,UAAU,WAAW,EAAG,UAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,IAAI,GAAG,KAAK;AACjF,QAAI,MAAM,EAAE,KAAK,OAAO;AACpB,UAAI,CAAC,GAAI,MAAK,MAAM,UAAU,MAAM,KAAK,MAAM,GAAG,CAAC;AACnD,SAAG,CAAC,IAAI,KAAK,CAAC;AAAA,IAClB;AAAA,EACJ;AACA,SAAO,GAAG,OAAO,MAAM,MAAM,UAAU,MAAM,KAAK,IAAI,CAAC;AACzD;;;AC5NA,IAAAC,UAAuB;;;ACAvB,IAAAC,SAAuB;;;ACDhB,IAAI,qBAAqB;AACzB,IAAI,qBAAqB;AACzB,IAAI,wBAAwB;AAK5B,IAAI,yBAAyB;;;ACM7B,SAAS,UAAU,KAAK,OAAO;AAClC,MAAI,OAAO,QAAQ,YAAY;AAC3B,QAAI,KAAK;AAAA,EACb,WACS,KAAK;AACV,QAAI,UAAU;AAAA,EAClB;AACA,SAAO;AACX;;;ACrBA,mBAAyB;AAelB,SAASC,gBAAe,cAAc,UAAU;AACnD,MAAI,UAAM,uBAAS,WAAY;AAAE,WAAQ;AAAA;AAAA,MAErC,OAAO;AAAA;AAAA,MAEP;AAAA;AAAA,MAEA,QAAQ;AAAA,QACJ,IAAI,UAAU;AACV,iBAAO,IAAI;AAAA,QACf;AAAA,QACA,IAAI,QAAQ,OAAO;AACf,cAAI,OAAO,IAAI;AACf,cAAI,SAAS,OAAO;AAChB,gBAAI,QAAQ;AACZ,gBAAI,SAAS,OAAO,IAAI;AAAA,UAC5B;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,EAAI,CAAC,EAAE,CAAC;AAER,MAAI,WAAW;AACf,SAAO,IAAI;AACf;;;ACtCA,IAAAC,SAAuB;AAGvB,IAAI,4BAA4B,OAAO,WAAW,cAAoB,yBAAwB;AAC9F,IAAI,gBAAgB,oBAAI,QAAQ;AAezB,SAAS,aAAa,MAAM,cAAc;AAC7C,MAAI,cAAcC,gBAAe,gBAAgB,MAAM,SAAU,UAAU;AACvE,WAAO,KAAK,QAAQ,SAAU,KAAK;AAAE,aAAO,UAAU,KAAK,QAAQ;AAAA,IAAG,CAAC;AAAA,EAC3E,CAAC;AAED,4BAA0B,WAAY;AAClC,QAAI,WAAW,cAAc,IAAI,WAAW;AAC5C,QAAI,UAAU;AACV,UAAI,aAAa,IAAI,IAAI,QAAQ;AACjC,UAAI,aAAa,IAAI,IAAI,IAAI;AAC7B,UAAI,YAAY,YAAY;AAC5B,iBAAW,QAAQ,SAAU,KAAK;AAC9B,YAAI,CAAC,WAAW,IAAI,GAAG,GAAG;AACtB,oBAAU,KAAK,IAAI;AAAA,QACvB;AAAA,MACJ,CAAC;AACD,iBAAW,QAAQ,SAAU,KAAK;AAC9B,YAAI,CAAC,WAAW,IAAI,GAAG,GAAG;AACtB,oBAAU,KAAK,SAAS;AAAA,QAC5B;AAAA,MACJ,CAAC;AAAA,IACL;AACA,kBAAc,IAAI,aAAa,IAAI;AAAA,EACvC,GAAG,CAAC,IAAI,CAAC;AACT,SAAO;AACX;;;AC3CA,IAAAC,SAAuB;;;ACDvB,IAAAC,gBAAoC;;;ACCpC,SAAS,KAAK,GAAG;AACb,SAAO;AACX;AACA,SAAS,kBAAkB,UAAU,YAAY;AAC7C,MAAI,eAAe,QAAQ;AAAE,iBAAa;AAAA,EAAM;AAChD,MAAI,SAAS,CAAC;AACd,MAAI,WAAW;AACf,MAAI,SAAS;AAAA,IACT,MAAM,WAAY;AACd,UAAI,UAAU;AACV,cAAM,IAAI,MAAM,kGAAkG;AAAA,MACtH;AACA,UAAI,OAAO,QAAQ;AACf,eAAO,OAAO,OAAO,SAAS,CAAC;AAAA,MACnC;AACA,aAAO;AAAA,IACX;AAAA,IACA,WAAW,SAAU,MAAM;AACvB,UAAI,OAAO,WAAW,MAAM,QAAQ;AACpC,aAAO,KAAK,IAAI;AAChB,aAAO,WAAY;AACf,iBAAS,OAAO,OAAO,SAAU,GAAG;AAAE,iBAAO,MAAM;AAAA,QAAM,CAAC;AAAA,MAC9D;AAAA,IACJ;AAAA,IACA,kBAAkB,SAAU,IAAI;AAC5B,iBAAW;AACX,aAAO,OAAO,QAAQ;AAClB,YAAI,MAAM;AACV,iBAAS,CAAC;AACV,YAAI,QAAQ,EAAE;AAAA,MAClB;AACA,eAAS;AAAA,QACL,MAAM,SAAU,GAAG;AAAE,iBAAO,GAAG,CAAC;AAAA,QAAG;AAAA,QACnC,QAAQ,WAAY;AAAE,iBAAO;AAAA,QAAQ;AAAA,MACzC;AAAA,IACJ;AAAA,IACA,cAAc,SAAU,IAAI;AACxB,iBAAW;AACX,UAAI,eAAe,CAAC;AACpB,UAAI,OAAO,QAAQ;AACf,YAAI,MAAM;AACV,iBAAS,CAAC;AACV,YAAI,QAAQ,EAAE;AACd,uBAAe;AAAA,MACnB;AACA,UAAI,eAAe,WAAY;AAC3B,YAAIC,OAAM;AACV,uBAAe,CAAC;AAChB,QAAAA,KAAI,QAAQ,EAAE;AAAA,MAClB;AACA,UAAI,QAAQ,WAAY;AAAE,eAAO,QAAQ,QAAQ,EAAE,KAAK,YAAY;AAAA,MAAG;AACvE,YAAM;AACN,eAAS;AAAA,QACL,MAAM,SAAU,GAAG;AACf,uBAAa,KAAK,CAAC;AACnB,gBAAM;AAAA,QACV;AAAA,QACA,QAAQ,SAAU,QAAQ;AACtB,yBAAe,aAAa,OAAO,MAAM;AACzC,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAMO,SAAS,oBAAoB,SAAS;AACzC,MAAI,YAAY,QAAQ;AAAE,cAAU,CAAC;AAAA,EAAG;AACxC,MAAI,SAAS,kBAAkB,IAAI;AACnC,SAAO,UAAU,SAAS,EAAE,OAAO,MAAM,KAAK,MAAM,GAAG,OAAO;AAC9D,SAAO;AACX;;;AC5EA,IAAAC,SAAuB;AACvB,IAAAC,gBAAkE;;;ACDlE,IAAAC,SAAuB;AACvB,IAAI,UAAU,SAAU,IAAI;AACxB,MAAI,UAAU,GAAG,SAAS,OAAO,OAAO,IAAI,CAAC,SAAS,CAAC;AACvD,MAAI,CAAC,SAAS;AACV,UAAM,IAAI,MAAM,oEAAoE;AAAA,EACxF;AACA,MAAI,SAAS,QAAQ,KAAK;AAC1B,MAAI,CAAC,QAAQ;AACT,UAAM,IAAI,MAAM,0BAA0B;AAAA,EAC9C;AACA,SAAa,qBAAc,QAAQ,SAAS,CAAC,GAAG,IAAI,CAAC;AACzD;AACA,QAAQ,kBAAkB;AACnB,SAAS,cAAc,QAAQ,UAAU;AAC5C,SAAO,UAAU,QAAQ;AACzB,SAAO;AACX;;;AChBO,IAAI,YAAY,oBAAoB;;;AVI3C,IAAI,UAAU,WAAY;AACtB;AACJ;AAIA,IAAI,eAAqB,kBAAW,SAAU,OAAO,WAAW;AAC5D,MAAI,MAAY,cAAO,IAAI;AAC3B,MAAI,KAAW,gBAAS;AAAA,IACpB,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,IAChB,oBAAoB;AAAA,EACxB,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC;AAC1C,MAAI,eAAe,MAAM,cAAc,WAAW,MAAM,UAAU,YAAY,MAAM,WAAW,kBAAkB,MAAM,iBAAiB,UAAU,MAAM,SAAS,SAAS,MAAM,QAAQ,UAAU,MAAM,SAAS,aAAa,MAAM,YAAY,cAAc,MAAM,aAAa,QAAQ,MAAM,OAAO,iBAAiB,MAAM,gBAAgB,KAAK,MAAM,IAAI,YAAY,OAAO,SAAS,QAAQ,IAAI,UAAU,MAAM,SAAS,OAAO,OAAO,OAAO,CAAC,gBAAgB,YAAY,aAAa,mBAAmB,WAAW,UAAU,WAAW,cAAc,eAAe,SAAS,kBAAkB,MAAM,SAAS,CAAC;AACvlB,MAAIC,WAAU;AACd,MAAI,eAAe,aAAa,CAAC,KAAK,SAAS,CAAC;AAChD,MAAI,iBAAiB,SAAS,SAAS,CAAC,GAAG,IAAI,GAAG,SAAS;AAC3D,SAAc;AAAA,IAAoB;AAAA,IAAU;AAAA,IACxC,WAAkB,qBAAcA,UAAS,EAAE,SAAS,WAAW,iBAAkC,QAAgB,YAAwB,aAA0B,OAAc,cAA4B,gBAAgB,CAAC,CAAC,gBAAgB,SAAS,KAAK,QAAiB,CAAC;AAAA,IAC/Q,eAAsB,oBAAmB,gBAAS,KAAK,QAAQ,GAAG,SAAS,SAAS,CAAC,GAAG,cAAc,GAAG,EAAE,KAAK,aAAa,CAAC,CAAC,IAAY,qBAAc,WAAW,SAAS,CAAC,GAAG,gBAAgB,EAAE,WAAsB,KAAK,aAAa,CAAC,GAAG,QAAQ;AAAA,EAAE;AACjQ,CAAC;AACD,aAAa,eAAe;AAAA,EACxB,SAAS;AAAA,EACT,iBAAiB;AAAA,EACjB,OAAO;AACX;AACA,aAAa,aAAa;AAAA,EACtB,WAAW;AAAA,EACX,WAAW;AACf;;;AWjCA,IAAAC,UAAuB;;;ACDvB,IAAAC,SAAuB;;;ACAvB,IAAAC,SAAuB;;;ACAvB,IAAI;AAIG,IAAI,WAAW,WAAY;AAC9B,MAAI,cAAc;AACd,WAAO;AAAA,EACX;AACA,MAAI,OAAO,sBAAsB,aAAa;AAC1C,WAAO;AAAA,EACX;AACA,SAAO;AACX;;;ACXA,SAAS,eAAe;AACpB,MAAI,CAAC;AACD,WAAO;AACX,MAAI,MAAM,SAAS,cAAc,OAAO;AACxC,MAAI,OAAO;AACX,MAAI,QAAQ,SAAS;AACrB,MAAI,OAAO;AACP,QAAI,aAAa,SAAS,KAAK;AAAA,EACnC;AACA,SAAO;AACX;AACA,SAAS,aAAa,KAAK,KAAK;AAE5B,MAAI,IAAI,YAAY;AAEhB,QAAI,WAAW,UAAU;AAAA,EAC7B,OACK;AACD,QAAI,YAAY,SAAS,eAAe,GAAG,CAAC;AAAA,EAChD;AACJ;AACA,SAAS,eAAe,KAAK;AACzB,MAAI,OAAO,SAAS,QAAQ,SAAS,qBAAqB,MAAM,EAAE,CAAC;AACnE,OAAK,YAAY,GAAG;AACxB;AACO,IAAI,sBAAsB,WAAY;AACzC,MAAI,UAAU;AACd,MAAI,aAAa;AACjB,SAAO;AAAA,IACH,KAAK,SAAU,OAAO;AAClB,UAAI,WAAW,GAAG;AACd,YAAK,aAAa,aAAa,GAAI;AAC/B,uBAAa,YAAY,KAAK;AAC9B,yBAAe,UAAU;AAAA,QAC7B;AAAA,MACJ;AACA;AAAA,IACJ;AAAA,IACA,QAAQ,WAAY;AAChB;AACA,UAAI,CAAC,WAAW,YAAY;AACxB,mBAAW,cAAc,WAAW,WAAW,YAAY,UAAU;AACrE,qBAAa;AAAA,MACjB;AAAA,IACJ;AAAA,EACJ;AACJ;;;AFpCO,IAAI,qBAAqB,WAAY;AACxC,MAAI,QAAQ,oBAAoB;AAChC,SAAO,SAAU,QAAQ,WAAW;AAChC,IAAM,iBAAU,WAAY;AACxB,YAAM,IAAI,MAAM;AAChB,aAAO,WAAY;AACf,cAAM,OAAO;AAAA,MACjB;AAAA,IACJ,GAAG,CAAC,UAAU,SAAS,CAAC;AAAA,EAC5B;AACJ;;;AGdO,IAAI,iBAAiB,WAAY;AACpC,MAAI,WAAW,mBAAmB;AAClC,MAAI,QAAQ,SAAU,IAAI;AACtB,QAAI,SAAS,GAAG,QAAQ,UAAU,GAAG;AACrC,aAAS,QAAQ,OAAO;AACxB,WAAO;AAAA,EACX;AACA,SAAO;AACX;;;ACfO,IAAI,UAAU;AAAA,EACjB,MAAM;AAAA,EACN,KAAK;AAAA,EACL,OAAO;AAAA,EACP,KAAK;AACT;AACA,IAAI,QAAQ,SAAU,GAAG;AAAE,SAAO,SAAS,KAAK,IAAI,EAAE,KAAK;AAAG;AAC9D,IAAI,YAAY,SAAU,SAAS;AAC/B,MAAI,KAAK,OAAO,iBAAiB,SAAS,IAAI;AAC9C,MAAI,OAAO,GAAG,YAAY,YAAY,gBAAgB,YAAY;AAClE,MAAI,MAAM,GAAG,YAAY,YAAY,eAAe,WAAW;AAC/D,MAAI,QAAQ,GAAG,YAAY,YAAY,iBAAiB,aAAa;AACrE,SAAO,CAAC,MAAM,IAAI,GAAG,MAAM,GAAG,GAAG,MAAM,KAAK,CAAC;AACjD;AACO,IAAI,cAAc,SAAU,SAAS;AACxC,MAAI,YAAY,QAAQ;AAAE,cAAU;AAAA,EAAU;AAC9C,MAAI,OAAO,WAAW,aAAa;AAC/B,WAAO;AAAA,EACX;AACA,MAAI,UAAU,UAAU,OAAO;AAC/B,MAAI,gBAAgB,SAAS,gBAAgB;AAC7C,MAAI,cAAc,OAAO;AACzB,SAAO;AAAA,IACH,MAAM,QAAQ,CAAC;AAAA,IACf,KAAK,QAAQ,CAAC;AAAA,IACd,OAAO,QAAQ,CAAC;AAAA,IAChB,KAAK,KAAK,IAAI,GAAG,cAAc,gBAAgB,QAAQ,CAAC,IAAI,QAAQ,CAAC,CAAC;AAAA,EAC1E;AACJ;;;ALxBA,IAAI,QAAQ,eAAe;AACpB,IAAI,gBAAgB;AAI3B,IAAI,YAAY,SAAU,IAAI,eAAe,SAAS,WAAW;AAC7D,MAAI,OAAO,GAAG,MAAM,MAAM,GAAG,KAAK,QAAQ,GAAG,OAAO,MAAM,GAAG;AAC7D,MAAI,YAAY,QAAQ;AAAE,cAAU;AAAA,EAAU;AAC9C,SAAO,QAAQ,OAAO,uBAAuB,0BAA0B,EAAE,OAAO,WAAW,uBAAuB,EAAE,OAAO,KAAK,KAAK,EAAE,OAAO,WAAW,iBAAiB,EAAE,OAAO,eAAe,4BAA4B,EAAE,OAAO,WAAW,4CAA4C,EAAE,OAAO;AAAA,IACnS,iBAAiB,sBAAsB,OAAO,WAAW,GAAG;AAAA,IAC5D,YAAY,YACR,uBAAuB,OAAO,MAAM,wBAAwB,EAAE,OAAO,KAAK,0BAA0B,EAAE,OAAO,OAAO,gEAAgE,EAAE,OAAO,KAAK,KAAK,EAAE,OAAO,WAAW,SAAS;AAAA,IACxO,YAAY,aAAa,kBAAkB,OAAO,KAAK,KAAK,EAAE,OAAO,WAAW,GAAG;AAAA,EACvF,EACK,OAAO,OAAO,EACd,KAAK,EAAE,GAAG,gBAAgB,EAAE,OAAO,oBAAoB,iBAAiB,EAAE,OAAO,KAAK,KAAK,EAAE,OAAO,WAAW,iBAAiB,EAAE,OAAO,oBAAoB,wBAAwB,EAAE,OAAO,KAAK,KAAK,EAAE,OAAO,WAAW,iBAAiB,EAAE,OAAO,oBAAoB,IAAI,EAAE,OAAO,oBAAoB,mBAAmB,EAAE,OAAO,WAAW,iBAAiB,EAAE,OAAO,oBAAoB,IAAI,EAAE,OAAO,oBAAoB,0BAA0B,EAAE,OAAO,WAAW,qBAAqB,EAAE,OAAO,eAAe,WAAW,EAAE,OAAO,wBAAwB,IAAI,EAAE,OAAO,KAAK,YAAY;AAC/kB;AACA,IAAI,uBAAuB,WAAY;AACnC,MAAI,UAAU,SAAS,SAAS,KAAK,aAAa,aAAa,KAAK,KAAK,EAAE;AAC3E,SAAO,SAAS,OAAO,IAAI,UAAU;AACzC;AACO,IAAI,mBAAmB,WAAY;AACtC,EAAM,iBAAU,WAAY;AACxB,aAAS,KAAK,aAAa,gBAAgB,qBAAqB,IAAI,GAAG,SAAS,CAAC;AACjF,WAAO,WAAY;AACf,UAAI,aAAa,qBAAqB,IAAI;AAC1C,UAAI,cAAc,GAAG;AACjB,iBAAS,KAAK,gBAAgB,aAAa;AAAA,MAC/C,OACK;AACD,iBAAS,KAAK,aAAa,eAAe,WAAW,SAAS,CAAC;AAAA,MACnE;AAAA,IACJ;AAAA,EACJ,GAAG,CAAC,CAAC;AACT;AAIO,IAAI,kBAAkB,SAAU,IAAI;AACvC,MAAI,aAAa,GAAG,YAAY,cAAc,GAAG,aAAa,KAAK,GAAG,SAAS,UAAU,OAAO,SAAS,WAAW;AACpH,mBAAiB;AAMjB,MAAI,MAAY,eAAQ,WAAY;AAAE,WAAO,YAAY,OAAO;AAAA,EAAG,GAAG,CAAC,OAAO,CAAC;AAC/E,SAAa,qBAAc,OAAO,EAAE,QAAQ,UAAU,KAAK,CAAC,YAAY,SAAS,CAAC,cAAc,eAAe,EAAE,EAAE,CAAC;AACxH;;;AMpDA,IAAI,mBAAmB;AACvB,IAAI,OAAO,WAAW,aAAa;AAC/B,MAAI;AACI,cAAU,OAAO,eAAe,CAAC,GAAG,WAAW;AAAA,MAC/C,KAAK,WAAY;AACb,2BAAmB;AACnB,eAAO;AAAA,MACX;AAAA,IACJ,CAAC;AAED,WAAO,iBAAiB,QAAQ,SAAS,OAAO;AAEhD,WAAO,oBAAoB,QAAQ,SAAS,OAAO;AAAA,EACvD,SACO,KAAK;AACR,uBAAmB;AAAA,EACvB;AACJ;AAdY;AAeL,IAAI,aAAa,mBAAmB,EAAE,SAAS,MAAM,IAAI;;;AClBhE,IAAI,uBAAuB,SAAU,MAAM;AAEvC,SAAO,KAAK,YAAY;AAC5B;AACA,IAAI,uBAAuB,SAAU,MAAM,UAAU;AACjD,MAAI,EAAE,gBAAgB,UAAU;AAC5B,WAAO;AAAA,EACX;AACA,MAAI,SAAS,OAAO,iBAAiB,IAAI;AACzC;AAAA;AAAA,IAEA,OAAO,QAAQ,MAAM;AAAA,IAEjB,EAAE,OAAO,cAAc,OAAO,aAAa,CAAC,qBAAqB,IAAI,KAAK,OAAO,QAAQ,MAAM;AAAA;AACvG;AACA,IAAI,0BAA0B,SAAU,MAAM;AAAE,SAAO,qBAAqB,MAAM,WAAW;AAAG;AAChG,IAAI,0BAA0B,SAAU,MAAM;AAAE,SAAO,qBAAqB,MAAM,WAAW;AAAG;AACzF,IAAI,0BAA0B,SAAU,MAAM,MAAM;AACvD,MAAI,gBAAgB,KAAK;AACzB,MAAI,UAAU;AACd,KAAG;AAEC,QAAI,OAAO,eAAe,eAAe,mBAAmB,YAAY;AACpE,gBAAU,QAAQ;AAAA,IACtB;AACA,QAAI,eAAe,uBAAuB,MAAM,OAAO;AACvD,QAAI,cAAc;AACd,UAAI,KAAK,mBAAmB,MAAM,OAAO,GAAG,eAAe,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC;AACrF,UAAI,eAAe,cAAc;AAC7B,eAAO;AAAA,MACX;AAAA,IACJ;AACA,cAAU,QAAQ;AAAA,EACtB,SAAS,WAAW,YAAY,cAAc;AAC9C,SAAO;AACX;AACA,IAAI,sBAAsB,SAAU,IAAI;AACpC,MAAI,YAAY,GAAG,WAAW,eAAe,GAAG,cAAc,eAAe,GAAG;AAChF,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AACA,IAAI,sBAAsB,SAAU,IAAI;AACpC,MAAI,aAAa,GAAG,YAAY,cAAc,GAAG,aAAa,cAAc,GAAG;AAC/E,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AACA,IAAI,yBAAyB,SAAU,MAAM,MAAM;AAC/C,SAAO,SAAS,MAAM,wBAAwB,IAAI,IAAI,wBAAwB,IAAI;AACtF;AACA,IAAI,qBAAqB,SAAU,MAAM,MAAM;AAC3C,SAAO,SAAS,MAAM,oBAAoB,IAAI,IAAI,oBAAoB,IAAI;AAC9E;AACA,IAAI,qBAAqB,SAAU,MAAM,WAAW;AAMhD,SAAO,SAAS,OAAO,cAAc,QAAQ,KAAK;AACtD;AACO,IAAI,eAAe,SAAU,MAAM,WAAW,OAAO,aAAa,cAAc;AACnF,MAAI,kBAAkB,mBAAmB,MAAM,OAAO,iBAAiB,SAAS,EAAE,SAAS;AAC3F,MAAI,QAAQ,kBAAkB;AAE9B,MAAI,SAAS,MAAM;AACnB,MAAI,eAAe,UAAU,SAAS,MAAM;AAC5C,MAAI,qBAAqB;AACzB,MAAI,kBAAkB,QAAQ;AAC9B,MAAI,kBAAkB;AACtB,MAAI,qBAAqB;AACzB,KAAG;AACC,QAAI,CAAC,QAAQ;AACT;AAAA,IACJ;AACA,QAAI,KAAK,mBAAmB,MAAM,MAAM,GAAG,WAAW,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC;AAC9F,QAAI,gBAAgB,WAAW,WAAW,kBAAkB;AAC5D,QAAI,YAAY,eAAe;AAC3B,UAAI,uBAAuB,MAAM,MAAM,GAAG;AACtC,2BAAmB;AACnB,8BAAsB;AAAA,MAC1B;AAAA,IACJ;AACA,QAAI,WAAW,OAAO;AAGtB,aAAU,YAAY,SAAS,aAAa,KAAK,yBAAyB,SAAS,OAAO;AAAA,EAC9F;AAAA;AAAA,IAEC,CAAC,gBAAgB,WAAW,SAAS;AAAA,IAEjC,iBAAiB,UAAU,SAAS,MAAM,KAAK,cAAc;AAAA;AAElE,MAAI,oBACE,gBAAgB,KAAK,IAAI,eAAe,IAAI,KAAO,CAAC,gBAAgB,QAAQ,kBAAmB;AACjG,yBAAqB;AAAA,EACzB,WACS,CAAC,oBACJ,gBAAgB,KAAK,IAAI,kBAAkB,IAAI,KAAO,CAAC,gBAAgB,CAAC,QAAQ,qBAAsB;AACxG,yBAAqB;AAAA,EACzB;AACA,SAAO;AACX;;;ARrGO,IAAI,aAAa,SAAU,OAAO;AACrC,SAAO,oBAAoB,QAAQ,CAAC,MAAM,eAAe,CAAC,EAAE,SAAS,MAAM,eAAe,CAAC,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC;AACjH;AACO,IAAI,aAAa,SAAU,OAAO;AAAE,SAAO,CAAC,MAAM,QAAQ,MAAM,MAAM;AAAG;AAChF,IAAI,aAAa,SAAU,KAAK;AAC5B,SAAO,OAAO,aAAa,MAAM,IAAI,UAAU;AACnD;AACA,IAAI,eAAe,SAAU,GAAG,GAAG;AAAE,SAAO,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC;AAAG;AAC5E,IAAI,gBAAgB,SAAU,IAAI;AAAE,SAAO,4BAA4B,OAAO,IAAI,mDAAmD,EAAE,OAAO,IAAI,2BAA2B;AAAG;AAChL,IAAI,YAAY;AAChB,IAAI,YAAY,CAAC;AACV,SAAS,oBAAoB,OAAO;AACvC,MAAI,qBAA2B,eAAO,CAAC,CAAC;AACxC,MAAI,gBAAsB,eAAO,CAAC,GAAG,CAAC,CAAC;AACvC,MAAI,aAAmB,eAAO;AAC9B,MAAI,KAAW,iBAAS,WAAW,EAAE,CAAC;AACtC,MAAIC,SAAc,iBAAS,cAAc,EAAE,CAAC;AAC5C,MAAI,YAAkB,eAAO,KAAK;AAClC,EAAM,kBAAU,WAAY;AACxB,cAAU,UAAU;AAAA,EACxB,GAAG,CAAC,KAAK,CAAC;AACV,EAAM,kBAAU,WAAY;AACxB,QAAI,MAAM,OAAO;AACb,eAAS,KAAK,UAAU,IAAI,uBAAuB,OAAO,EAAE,CAAC;AAC7D,UAAI,UAAU,cAAc,CAAC,MAAM,QAAQ,OAAO,IAAI,MAAM,UAAU,CAAC,GAAG,IAAI,UAAU,GAAG,IAAI,EAAE,OAAO,OAAO;AAC/G,cAAQ,QAAQ,SAAU,IAAI;AAAE,eAAO,GAAG,UAAU,IAAI,uBAAuB,OAAO,EAAE,CAAC;AAAA,MAAG,CAAC;AAC7F,aAAO,WAAY;AACf,iBAAS,KAAK,UAAU,OAAO,uBAAuB,OAAO,EAAE,CAAC;AAChE,gBAAQ,QAAQ,SAAU,IAAI;AAAE,iBAAO,GAAG,UAAU,OAAO,uBAAuB,OAAO,EAAE,CAAC;AAAA,QAAG,CAAC;AAAA,MACpG;AAAA,IACJ;AACA;AAAA,EACJ,GAAG,CAAC,MAAM,OAAO,MAAM,QAAQ,SAAS,MAAM,MAAM,CAAC;AACrD,MAAI,oBAA0B,oBAAY,SAAU,OAAO,QAAQ;AAC/D,QAAK,aAAa,SAAS,MAAM,QAAQ,WAAW,KAAO,MAAM,SAAS,WAAW,MAAM,SAAU;AACjG,aAAO,CAAC,UAAU,QAAQ;AAAA,IAC9B;AACA,QAAI,QAAQ,WAAW,KAAK;AAC5B,QAAI,aAAa,cAAc;AAC/B,QAAI,SAAS,YAAY,QAAQ,MAAM,SAAS,WAAW,CAAC,IAAI,MAAM,CAAC;AACvE,QAAI,SAAS,YAAY,QAAQ,MAAM,SAAS,WAAW,CAAC,IAAI,MAAM,CAAC;AACvE,QAAI;AACJ,QAAI,SAAS,MAAM;AACnB,QAAI,gBAAgB,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM,IAAI,MAAM;AAEhE,QAAI,aAAa,SAAS,kBAAkB,OAAO,OAAO,SAAS,SAAS;AACxE,aAAO;AAAA,IACX;AACA,QAAI,+BAA+B,wBAAwB,eAAe,MAAM;AAChF,QAAI,CAAC,8BAA8B;AAC/B,aAAO;AAAA,IACX;AACA,QAAI,8BAA8B;AAC9B,oBAAc;AAAA,IAClB,OACK;AACD,oBAAc,kBAAkB,MAAM,MAAM;AAC5C,qCAA+B,wBAAwB,eAAe,MAAM;AAAA,IAEhF;AACA,QAAI,CAAC,8BAA8B;AAC/B,aAAO;AAAA,IACX;AACA,QAAI,CAAC,WAAW,WAAW,oBAAoB,UAAU,UAAU,SAAS;AACxE,iBAAW,UAAU;AAAA,IACzB;AACA,QAAI,CAAC,aAAa;AACd,aAAO;AAAA,IACX;AACA,QAAI,gBAAgB,WAAW,WAAW;AAC1C,WAAO,aAAa,eAAe,QAAQ,OAAO,kBAAkB,MAAM,SAAS,QAAQ,IAAI;AAAA,EACnG,GAAG,CAAC,CAAC;AACL,MAAI,gBAAsB,oBAAY,SAAU,QAAQ;AACpD,QAAI,QAAQ;AACZ,QAAI,CAAC,UAAU,UAAU,UAAU,UAAU,SAAS,CAAC,MAAMA,QAAO;AAEhE;AAAA,IACJ;AACA,QAAI,QAAQ,YAAY,QAAQ,WAAW,KAAK,IAAI,WAAW,KAAK;AACpE,QAAI,cAAc,mBAAmB,QAAQ,OAAO,SAAU,GAAG;AAAE,aAAO,EAAE,SAAS,MAAM,SAAS,EAAE,WAAW,MAAM,UAAU,MAAM,WAAW,EAAE,iBAAiB,aAAa,EAAE,OAAO,KAAK;AAAA,IAAG,CAAC,EAAE,CAAC;AAEvM,QAAI,eAAe,YAAY,QAAQ;AACnC,UAAI,MAAM,YAAY;AAClB,cAAM,eAAe;AAAA,MACzB;AACA;AAAA,IACJ;AAEA,QAAI,CAAC,aAAa;AACd,UAAI,cAAc,UAAU,QAAQ,UAAU,CAAC,GAC1C,IAAI,UAAU,EACd,OAAO,OAAO,EACd,OAAO,SAAU,MAAM;AAAE,eAAO,KAAK,SAAS,MAAM,MAAM;AAAA,MAAG,CAAC;AACnE,UAAI,aAAa,WAAW,SAAS,IAAI,kBAAkB,OAAO,WAAW,CAAC,CAAC,IAAI,CAAC,UAAU,QAAQ;AACtG,UAAI,YAAY;AACZ,YAAI,MAAM,YAAY;AAClB,gBAAM,eAAe;AAAA,QACzB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,GAAG,CAAC,CAAC;AACL,MAAI,eAAqB,oBAAY,SAAU,MAAM,OAAO,QAAQ,QAAQ;AACxE,QAAI,QAAQ,EAAE,MAAY,OAAc,QAAgB,QAAgB,cAAc,yBAAyB,MAAM,EAAE;AACvH,uBAAmB,QAAQ,KAAK,KAAK;AACrC,eAAW,WAAY;AACnB,yBAAmB,UAAU,mBAAmB,QAAQ,OAAO,SAAU,GAAG;AAAE,eAAO,MAAM;AAAA,MAAO,CAAC;AAAA,IACvG,GAAG,CAAC;AAAA,EACR,GAAG,CAAC,CAAC;AACL,MAAI,mBAAyB,oBAAY,SAAU,OAAO;AACtD,kBAAc,UAAU,WAAW,KAAK;AACxC,eAAW,UAAU;AAAA,EACzB,GAAG,CAAC,CAAC;AACL,MAAI,cAAoB,oBAAY,SAAU,OAAO;AACjD,iBAAa,MAAM,MAAM,WAAW,KAAK,GAAG,MAAM,QAAQ,kBAAkB,OAAO,MAAM,QAAQ,OAAO,CAAC;AAAA,EAC7G,GAAG,CAAC,CAAC;AACL,MAAI,kBAAwB,oBAAY,SAAU,OAAO;AACrD,iBAAa,MAAM,MAAM,WAAW,KAAK,GAAG,MAAM,QAAQ,kBAAkB,OAAO,MAAM,QAAQ,OAAO,CAAC;AAAA,EAC7G,GAAG,CAAC,CAAC;AACL,EAAM,kBAAU,WAAY;AACxB,cAAU,KAAKA,MAAK;AACpB,UAAM,aAAa;AAAA,MACf,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,oBAAoB;AAAA,IACxB,CAAC;AACD,aAAS,iBAAiB,SAAS,eAAe,UAAU;AAC5D,aAAS,iBAAiB,aAAa,eAAe,UAAU;AAChE,aAAS,iBAAiB,cAAc,kBAAkB,UAAU;AACpE,WAAO,WAAY;AACf,kBAAY,UAAU,OAAO,SAAU,MAAM;AAAE,eAAO,SAASA;AAAA,MAAO,CAAC;AACvE,eAAS,oBAAoB,SAAS,eAAe,UAAU;AAC/D,eAAS,oBAAoB,aAAa,eAAe,UAAU;AACnE,eAAS,oBAAoB,cAAc,kBAAkB,UAAU;AAAA,IAC3E;AAAA,EACJ,GAAG,CAAC,CAAC;AACL,MAAI,kBAAkB,MAAM,iBAAiB,QAAQ,MAAM;AAC3D,SAAc;AAAA,IAAoB;AAAA,IAAU;AAAA,IACxC,QAAc,sBAAcA,QAAO,EAAE,QAAQ,cAAc,EAAE,EAAE,CAAC,IAAI;AAAA,IACpE,kBAAwB,sBAAc,iBAAiB,EAAE,YAAY,MAAM,YAAY,SAAS,MAAM,QAAQ,CAAC,IAAI;AAAA,EAAI;AAC/H;AACA,SAAS,yBAAyB,MAAM;AACpC,MAAI,eAAe;AACnB,SAAO,SAAS,MAAM;AAClB,QAAI,gBAAgB,YAAY;AAC5B,qBAAe,KAAK;AACpB,aAAO,KAAK;AAAA,IAChB;AACA,WAAO,KAAK;AAAA,EAChB;AACA,SAAO;AACX;;;ASzJA,IAAO,kBAAQ,cAAc,WAAW,mBAAmB;;;ArBC3D,IAAI,oBAA0B,mBAAW,SAAU,OAAO,KAAK;AAAE,SAAc,sBAAc,cAAc,SAAS,CAAC,GAAG,OAAO,EAAE,KAAU,SAAS,gBAAQ,CAAC,CAAC;AAAI,CAAC;AACnK,kBAAkB,aAAa,aAAa;AAC5C,IAAO,sBAAQ;;;AsBNf,IAAI,mBAAmB,SAAU,gBAAgB;AAC7C,MAAI,OAAO,aAAa,aAAa;AACjC,WAAO;AAAA,EACX;AACA,MAAI,eAAe,MAAM,QAAQ,cAAc,IAAI,eAAe,CAAC,IAAI;AACvE,SAAO,aAAa,cAAc;AACtC;AACA,IAAI,aAAa,oBAAI,QAAQ;AAC7B,IAAI,oBAAoB,oBAAI,QAAQ;AACpC,IAAI,YAAY,CAAC;AACjB,IAAI,YAAY;AAChB,IAAI,aAAa,SAAU,MAAM;AAC7B,SAAO,SAAS,KAAK,QAAQ,WAAW,KAAK,UAAU;AAC3D;AACA,IAAI,iBAAiB,SAAU,QAAQ,SAAS;AAC5C,SAAO,QACF,IAAI,SAAU,QAAQ;AACvB,QAAI,OAAO,SAAS,MAAM,GAAG;AACzB,aAAO;AAAA,IACX;AACA,QAAI,kBAAkB,WAAW,MAAM;AACvC,QAAI,mBAAmB,OAAO,SAAS,eAAe,GAAG;AACrD,aAAO;AAAA,IACX;AACA,YAAQ,MAAM,eAAe,QAAQ,2BAA2B,QAAQ,iBAAiB;AACzF,WAAO;AAAA,EACX,CAAC,EACI,OAAO,SAAU,GAAG;AAAE,WAAO,QAAQ,CAAC;AAAA,EAAG,CAAC;AACnD;AASA,IAAI,yBAAyB,SAAU,gBAAgB,YAAY,YAAY,kBAAkB;AAC7F,MAAI,UAAU,eAAe,YAAY,MAAM,QAAQ,cAAc,IAAI,iBAAiB,CAAC,cAAc,CAAC;AAC1G,MAAI,CAAC,UAAU,UAAU,GAAG;AACxB,cAAU,UAAU,IAAI,oBAAI,QAAQ;AAAA,EACxC;AACA,MAAI,gBAAgB,UAAU,UAAU;AACxC,MAAI,cAAc,CAAC;AACnB,MAAI,iBAAiB,oBAAI,IAAI;AAC7B,MAAI,iBAAiB,IAAI,IAAI,OAAO;AACpC,MAAI,OAAO,SAAU,IAAI;AACrB,QAAI,CAAC,MAAM,eAAe,IAAI,EAAE,GAAG;AAC/B;AAAA,IACJ;AACA,mBAAe,IAAI,EAAE;AACrB,SAAK,GAAG,UAAU;AAAA,EACtB;AACA,UAAQ,QAAQ,IAAI;AACpB,MAAI,OAAO,SAAU,QAAQ;AACzB,QAAI,CAAC,UAAU,eAAe,IAAI,MAAM,GAAG;AACvC;AAAA,IACJ;AACA,UAAM,UAAU,QAAQ,KAAK,OAAO,UAAU,SAAU,MAAM;AAC1D,UAAI,eAAe,IAAI,IAAI,GAAG;AAC1B,aAAK,IAAI;AAAA,MACb,OACK;AACD,YAAI;AACA,cAAI,OAAO,KAAK,aAAa,gBAAgB;AAC7C,cAAI,gBAAgB,SAAS,QAAQ,SAAS;AAC9C,cAAI,gBAAgB,WAAW,IAAI,IAAI,KAAK,KAAK;AACjD,cAAI,eAAe,cAAc,IAAI,IAAI,KAAK,KAAK;AACnD,qBAAW,IAAI,MAAM,YAAY;AACjC,wBAAc,IAAI,MAAM,WAAW;AACnC,sBAAY,KAAK,IAAI;AACrB,cAAI,iBAAiB,KAAK,eAAe;AACrC,8BAAkB,IAAI,MAAM,IAAI;AAAA,UACpC;AACA,cAAI,gBAAgB,GAAG;AACnB,iBAAK,aAAa,YAAY,MAAM;AAAA,UACxC;AACA,cAAI,CAAC,eAAe;AAChB,iBAAK,aAAa,kBAAkB,MAAM;AAAA,UAC9C;AAAA,QACJ,SACO,GAAG;AACN,kBAAQ,MAAM,mCAAmC,MAAM,CAAC;AAAA,QAC5D;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AACA,OAAK,UAAU;AACf,iBAAe,MAAM;AACrB;AACA,SAAO,WAAY;AACf,gBAAY,QAAQ,SAAU,MAAM;AAChC,UAAI,eAAe,WAAW,IAAI,IAAI,IAAI;AAC1C,UAAI,cAAc,cAAc,IAAI,IAAI,IAAI;AAC5C,iBAAW,IAAI,MAAM,YAAY;AACjC,oBAAc,IAAI,MAAM,WAAW;AACnC,UAAI,CAAC,cAAc;AACf,YAAI,CAAC,kBAAkB,IAAI,IAAI,GAAG;AAC9B,eAAK,gBAAgB,gBAAgB;AAAA,QACzC;AACA,0BAAkB,OAAO,IAAI;AAAA,MACjC;AACA,UAAI,CAAC,aAAa;AACd,aAAK,gBAAgB,UAAU;AAAA,MACnC;AAAA,IACJ,CAAC;AACD;AACA,QAAI,CAAC,WAAW;AAEZ,mBAAa,oBAAI,QAAQ;AACzB,mBAAa,oBAAI,QAAQ;AACzB,0BAAoB,oBAAI,QAAQ;AAChC,kBAAY,CAAC;AAAA,IACjB;AAAA,EACJ;AACJ;AAQO,IAAI,aAAa,SAAU,gBAAgB,YAAY,YAAY;AACtE,MAAI,eAAe,QAAQ;AAAE,iBAAa;AAAA,EAAoB;AAC9D,MAAI,UAAU,MAAM,KAAK,MAAM,QAAQ,cAAc,IAAI,iBAAiB,CAAC,cAAc,CAAC;AAC1F,MAAI,mBAAmB,cAAc,iBAAiB,cAAc;AACpE,MAAI,CAAC,kBAAkB;AACnB,WAAO,WAAY;AAAE,aAAO;AAAA,IAAM;AAAA,EACtC;AAGA,UAAQ,KAAK,MAAM,SAAS,MAAM,KAAK,iBAAiB,iBAAiB,qBAAqB,CAAC,CAAC;AAChG,SAAO,uBAAuB,SAAS,kBAAkB,YAAY,aAAa;AACtF;", "names": ["handleFocusIn", "handleFocusOut", "handleMutations", "container", "React", "__assign", "React", "React", "useCallbackRef", "React", "useCallbackRef", "React", "import_react", "cbs", "React", "import_react", "React", "SideCar", "React", "React", "React", "Style"]}