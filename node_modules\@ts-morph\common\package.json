{"name": "@ts-morph/common", "version": "0.12.3", "description": "Common functionality for ts-morph packages.", "main": "dist/ts-morph-common.js", "author": "<PERSON>", "license": "MIT", "repository": "git+https://github.com/dsherret/ts-morph.git", "typings": "lib/ts-morph-common.d.ts", "scripts": {"build": "npm run build:declarations && npm run build:node && npm run build:deno", "build:node": "rimraf dist && npm run createLibFile && npm run rollup && deno run -A scripts/bundleLocalTs.ts", "build:deno": "rimraf ../../deno/common && rimraf dist-deno && npm run rollup -- --environment BUILD:deno && npm run build:declarations && deno run -A scripts/buildDeno.ts", "build:declarations": "deno run -A scripts/buildDeclarations.ts", "createLibFile": "deno run -A scripts/createLibFile.ts", "test": "cross-env TS_NODE_TRANSPILE_ONLY=\"true\" mocha", "test:ci": "npm run test", "test:debug": "npm run test --inspect-brk", "rollup": "rollup --config"}, "dependencies": {"fast-glob": "^3.2.7", "mkdirp": "^1.0.4", "minimatch": "^3.0.4", "path-browserify": "^1.0.1"}, "devDependencies": {"@types/chai": "^4.2.22", "@types/mkdirp": "^1.0.2", "@types/mocha": "^9.0.0", "@types/minimatch": "^3.0.5", "@types/node": "^16.11.7", "chai": "^4.3.4", "cross-env": "^7.0.3", "mocha": "^9.1.3", "rimraf": "^3.0.2", "rollup": "^2.60.0", "rollup-plugin-typescript2": "^0.31.0", "ts-node": "^10.4.0", "typescript": "4.5.5"}, "publishConfig": {"access": "public"}, "browser": {"fs": false, "os": false, "fs.realpath": false, "mkdirp": false, "dir-glob": false, "graceful-fs": false, "fast-glob": false, "source-map-support": false, "glob-parent": false, "glob": false, "path": false, "crypto": false, "buffer": false, "@microsoft/typescript-etw": false, "inspector": false}}