{"version": "3.2.4", "results": [[":src/tests/lib/validations.test.ts", {"duration": 21.66190000000006, "failed": false}], [":src/tests/hooks/useFormMutation.test.tsx", {"duration": 778.6788000000001, "failed": false}], [":src/tests/hooks/useCrudOperations.test.tsx", {"duration": 675.4688999999998, "failed": false}], [":src/tests/lib/formatters.test.ts", {"duration": 69.96160000000009, "failed": false}], [":src/hooks/__tests__/useAIChat.test.tsx", {"duration": 5553.9301000000005, "failed": true}], [":src/components/orcamento/__tests__/SinapiSelector.test.tsx", {"duration": 0, "failed": true}], [":src/hooks/__tests__/useFormMutation.test.tsx", {"duration": 174.91759999999977, "failed": true}], [":src/components/contratos/__tests__/ContratoFormSection.test.tsx", {"duration": 7240.847900000001, "failed": true}], [":src/tests/useObras.test.tsx", {"duration": 0, "failed": true}], [":src/components/orcamento/__tests__/WizardOrcamentoRefactored.test.tsx", {"duration": 0, "failed": true}], [":src/hooks/__tests__/useWizardOrcamento.test.tsx", {"duration": 0, "failed": true}], [":src/hooks/__tests__/useContratoAI.test.tsx", {"duration": 0, "failed": true}], [":src/components/error/__tests__/ErrorHandling.test.tsx", {"duration": 0, "failed": true}], [":src/hooks/__tests__/useCrudOperations.test.tsx", {"duration": 3194.4643, "failed": true}], [":src/tests/integration/edge-functions.test.ts", {"duration": 249.61070000000018, "failed": false}], [":src/lib/validations/auth.test.ts", {"duration": 30.85089999999991, "failed": true}], [":src/lib/validations/despesa.test.ts", {"duration": 21.275899999999638, "failed": false}], [":src/lib/validations/orcamento.test.ts", {"duration": 26.73070000000007, "failed": false}], [":src/tests/accessibility/basic.accessibility.test.tsx", {"duration": 602.5772999999999, "failed": false}], [":src/lib/validations/construtora.test.ts", {"duration": 24.73129999999992, "failed": true}], [":src/lib/validations/fornecedor.test.ts", {"duration": 22.686899999999923, "failed": true}], [":src/components/auth/__tests__/RegisterForm.test.tsx", {"duration": 2538.9269, "failed": true}], [":src/pages/dashboard/obras/__tests__/NovaObraRefactored.test.tsx", {"duration": 44.57580000000053, "failed": true}], [":src/lib/validations/obra.test.ts", {"duration": 23.166500000000042, "failed": true}], [":src/tests/utils.test.ts", {"duration": 57.872299999999996, "failed": false}], [":src/pages/dashboard/construtoras/__tests__/NovaConstrutora.test.tsx", {"duration": 97.9373999999998, "failed": true}], [":src/tests/accessibility/components/Header.accessibility.test.tsx", {"duration": 250.9002999999998, "failed": false}], [":src/lib/validations/nota-fiscal.test.ts", {"duration": 26.940000000000055, "failed": true}], [":src/tests/components/FornecedoresChat.test.tsx", {"duration": 0, "failed": true}], [":src/tests/accessibility/components/LandingPage.accessibility.test.tsx", {"duration": 263.6704, "failed": false}], [":src/tests/accessibility/components/Images.accessibility.test.tsx", {"duration": 223.87739999999985, "failed": false}], [":src/tests/accessibility/LandingPage.accessibility.test.tsx", {"duration": 78.11610000000019, "failed": false}], [":src/tests/accessibility/components/Forms.accessibility.test.tsx", {"duration": 219.78420000000006, "failed": false}], [":src/lib/validations/__tests__/condominio.test.ts", {"duration": 20.634900000000016, "failed": false}], [":src/hooks/__tests__/useObrasCondominio.test.tsx", {"duration": 24.260999999999967, "failed": false}], [":src/hooks/__tests__/useObrasCondominio.integration.test.tsx", {"duration": 6.944800000000214, "failed": true}], [":src/components/obras/__tests__/CondomínioFormSection.test.tsx", {"duration": 260.2020000000002, "failed": false}], [":src/tests/integration/condominio.integration.test.tsx", {"duration": 82.6356640001759, "failed": true}], [":src/components/obras/__tests__/CondominioDashboard.test.tsx", {"duration": 102.62835699971765, "failed": true}]]}