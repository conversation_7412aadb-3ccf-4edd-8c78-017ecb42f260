"use strict";Object.defineProperty(exports, "__esModule", { value: true });exports.default = void 0;

var _vite = await jitiImport("vite");
var _pluginReactSwc = _interopRequireDefault(await jitiImport("@vitejs/plugin-react-swc"));
var _path = _interopRequireDefault(await jitiImport("path"));
var _rollupPluginVisualizer = await jitiImport("rollup-plugin-visualizer");function _interopRequireDefault(e) {return e && e.__esModule ? e : { default: e };} /// <reference types="vitest" />

// https://vitejs.dev/config/
var _default = exports.default = (0, _vite.defineConfig)(({ command }) => ({
  server: {
    host: "::",
    port: 8080,
    headers: {
      'Cross-Origin-Embedder-Policy': 'cross-origin',
      'Cross-Origin-Opener-Policy': 'same-origin'
    }
  },
  plugins: [
  (0, _pluginReactSwc.default)(),
  command === 'build' && (0, _rollupPluginVisualizer.visualizer)({
    filename: 'dist/stats.html',
    open: true,
    gzipSize: true,
    brotliSize: true
  })].
  filter(Boolean),
  resolve: {
    alias: {
      "@": _path.default.resolve(__dirname, "./src")
    }
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          'react-vendor': ['react', 'react-dom'],
          'ui-vendor': ['@radix-ui/react-select', '@radix-ui/react-dialog', 'framer-motion'],
          'query-vendor': ['@tanstack/react-query']
        }
      }
    }
  },
  test: {
    globals: true,
    environment: "jsdom",
    setupFiles: "./src/tests/setup.ts",
    css: true
  },
  optimizeDeps: {
    include: ['react', 'react-dom', '@tanstack/react-query', 'framer-motion']
  }
})); /* v9-93087bbf79c7d221 */
