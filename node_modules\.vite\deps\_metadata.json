{"hash": "0e141224", "configHash": "95a00458", "lockfileHash": "ecbfa977", "browserHash": "8e74b594", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "d6c6ba4e", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "21e84821", "needsInterop": true}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "7618d671", "needsInterop": false}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "df09990a", "needsInterop": false}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "249aeba9", "needsInterop": true}, "@hookform/resolvers/zod": {"src": "../../@hookform/resolvers/zod/dist/zod.mjs", "file": "@hookform_resolvers_zod.js", "fileHash": "31d084c8", "needsInterop": false}, "@radix-ui/react-alert-dialog": {"src": "../../@radix-ui/react-alert-dialog/dist/index.mjs", "file": "@radix-ui_react-alert-dialog.js", "fileHash": "84f61d38", "needsInterop": false}, "@radix-ui/react-avatar": {"src": "../../@radix-ui/react-avatar/dist/index.mjs", "file": "@radix-ui_react-avatar.js", "fileHash": "41537848", "needsInterop": false}, "@radix-ui/react-checkbox": {"src": "../../@radix-ui/react-checkbox/dist/index.mjs", "file": "@radix-ui_react-checkbox.js", "fileHash": "5190685d", "needsInterop": false}, "@radix-ui/react-collapsible": {"src": "../../@radix-ui/react-collapsible/dist/index.mjs", "file": "@radix-ui_react-collapsible.js", "fileHash": "c2f6c573", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "83ac1bcf", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "013c2905", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "9707b07b", "needsInterop": false}, "@radix-ui/react-popover": {"src": "../../@radix-ui/react-popover/dist/index.mjs", "file": "@radix-ui_react-popover.js", "fileHash": "bbe8b9ec", "needsInterop": false}, "@radix-ui/react-progress": {"src": "../../@radix-ui/react-progress/dist/index.mjs", "file": "@radix-ui_react-progress.js", "fileHash": "f8bf2c0c", "needsInterop": false}, "@radix-ui/react-radio-group": {"src": "../../@radix-ui/react-radio-group/dist/index.mjs", "file": "@radix-ui_react-radio-group.js", "fileHash": "d64a41c3", "needsInterop": false}, "@radix-ui/react-scroll-area": {"src": "../../@radix-ui/react-scroll-area/dist/index.mjs", "file": "@radix-ui_react-scroll-area.js", "fileHash": "968f092f", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "6d9de33f", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "102be97a", "needsInterop": false}, "@radix-ui/react-slider": {"src": "../../@radix-ui/react-slider/dist/index.mjs", "file": "@radix-ui_react-slider.js", "fileHash": "5cd635c9", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "029bbb61", "needsInterop": false}, "@radix-ui/react-switch": {"src": "../../@radix-ui/react-switch/dist/index.mjs", "file": "@radix-ui_react-switch.js", "fileHash": "068f94dc", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "1a6b7eed", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "2d6f2461", "needsInterop": false}, "@supabase/supabase-js": {"src": "../../@supabase/supabase-js/dist/module/index.js", "file": "@supabase_supabase-js.js", "fileHash": "bcebde96", "needsInterop": false}, "@tanstack/react-table": {"src": "../../@tanstack/react-table/build/lib/index.mjs", "file": "@tanstack_react-table.js", "fileHash": "03e26f50", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "24efa0a4", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "354ed1fd", "needsInterop": false}, "cmdk": {"src": "../../cmdk/dist/index.mjs", "file": "cmdk.js", "fileHash": "ab3e2f03", "needsInterop": false}, "crypto-js": {"src": "../../crypto-js/index.js", "file": "crypto-js.js", "fileHash": "81be9008", "needsInterop": true}, "date-fns": {"src": "../../date-fns/index.mjs", "file": "date-fns.js", "fileHash": "611e4bd2", "needsInterop": false}, "date-fns/locale": {"src": "../../date-fns/locale.mjs", "file": "date-fns_locale.js", "fileHash": "82e980d8", "needsInterop": false}, "dompurify": {"src": "../../dompurify/dist/purify.es.mjs", "file": "dompurify.js", "fileHash": "b311a54b", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "6ffd9fd8", "needsInterop": false}, "react-day-picker": {"src": "../../react-day-picker/dist/index.esm.js", "file": "react-day-picker.js", "fileHash": "4406a062", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "f29cefdc", "needsInterop": true}, "react-helmet-async": {"src": "../../react-helmet-async/lib/index.esm.js", "file": "react-helmet-async.js", "fileHash": "931a75b8", "needsInterop": false}, "react-hook-form": {"src": "../../react-hook-form/dist/index.esm.mjs", "file": "react-hook-form.js", "fileHash": "07aee01e", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "adbd7a82", "needsInterop": false}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "892316d6", "needsInterop": true}, "recharts": {"src": "../../recharts/es6/index.js", "file": "recharts.js", "fileHash": "a6bf3885", "needsInterop": false}, "sonner": {"src": "../../sonner/dist/index.mjs", "file": "sonner.js", "fileHash": "c09fe31e", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "c7e87b8c", "needsInterop": false}, "zod": {"src": "../../zod/index.js", "file": "zod.js", "fileHash": "f6936a71", "needsInterop": false}}, "chunks": {"browser-BFWNBQMC": {"file": "browser-BFWNBQMC.js"}, "chunk-CKPFX26C": {"file": "chunk-CKPFX26C.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-FB4RGJQ3": {"file": "chunk-FB4RGJQ3.js"}, "chunk-A55GUTZU": {"file": "chunk-A55GUTZU.js"}, "chunk-SAK6X3UH": {"file": "chunk-SAK6X3UH.js"}, "chunk-3AA2TT25": {"file": "chunk-3AA2TT25.js"}, "chunk-6ZMM2PAV": {"file": "chunk-6ZMM2PAV.js"}, "chunk-6EYFWQL7": {"file": "chunk-6EYFWQL7.js"}, "chunk-QZUMF3DX": {"file": "chunk-QZUMF3DX.js"}, "chunk-NQ3MXSWR": {"file": "chunk-NQ3MXSWR.js"}, "chunk-DTOW77JG": {"file": "chunk-DTOW77JG.js"}, "chunk-PQOWYBBD": {"file": "chunk-PQOWYBBD.js"}, "chunk-U4NFJIXU": {"file": "chunk-U4NFJIXU.js"}, "chunk-EOLSW7PA": {"file": "chunk-EOLSW7PA.js"}, "chunk-LDEM2FB3": {"file": "chunk-LDEM2FB3.js"}, "chunk-G27BODWR": {"file": "chunk-G27BODWR.js"}, "chunk-YNJSWSDM": {"file": "chunk-YNJSWSDM.js"}, "chunk-3MC66YHI": {"file": "chunk-3MC66YHI.js"}, "chunk-IOKNWUZX": {"file": "chunk-IOKNWUZX.js"}, "chunk-ZEYZZQRY": {"file": "chunk-ZEYZZQRY.js"}, "chunk-CIEJFPOE": {"file": "chunk-CIEJFPOE.js"}, "chunk-5Q5YC75F": {"file": "chunk-5Q5YC75F.js"}, "chunk-MADJVYWR": {"file": "chunk-MADJVYWR.js"}, "chunk-XOUBXFGS": {"file": "chunk-XOUBXFGS.js"}, "chunk-JLQW6FOD": {"file": "chunk-JLQW6FOD.js"}, "chunk-SBXSPTCP": {"file": "chunk-SBXSPTCP.js"}, "chunk-NFC5BX5N": {"file": "chunk-NFC5BX5N.js"}, "chunk-7BUGFXDR": {"file": "chunk-7BUGFXDR.js"}, "chunk-CMM6OKGN": {"file": "chunk-CMM6OKGN.js"}, "chunk-OL46QLBJ": {"file": "chunk-OL46QLBJ.js"}}}